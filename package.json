{"name": "content-saas", "version": "1.0.0", "description": "The Ultimate Content Writing SaaS Platform", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:push": "prisma db push", "db:studio": "prisma studio", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "test:rotation": "node scripts/test-instant-rotation.mjs", "test:topics": "node scripts/test-topic-classification.mjs", "test:writing": "node scripts/test-writing-style.mjs", "test:voices": "node scripts/test-voice-selection.mjs", "test:enhanced": "node scripts/test-enhanced-analysis.mjs", "test:simple": "node scripts/test-simple-writing.mjs", "demo:simple": "node scripts/test-simple-writing-demo.mjs", "test:wordcount": "node scripts/test-word-count.mjs", "demo:wordcount": "node scripts/demo-word-count.mjs", "reset:quotas": "node scripts/reset-quotas.mjs", "test:retries": "node scripts/test-unlimited-retries.mjs", "demo:retries": "node scripts/demo-unlimited-retries.mjs", "test:fixes": "node scripts/test-error-fixes.mjs", "demo:youtube": "node scripts/test-youtube-narration.mjs", "test:auth": "node scripts/test-auth-optimization.mjs", "test:relaxation": "node scripts/test-wordcount-relaxation.mjs", "test:tavily": "node scripts/test-tavily-api-keys.mjs", "update:tavily": "node scripts/update-tavily-keys.mjs", "update:tavily:auto": "node scripts/update-tavily-keys.mjs --auto", "test:daily-auth": "node scripts/test-daily-auth.mjs", "test:content": "node scripts/test-content-access.mjs", "verify:content": "node scripts/verify-content-system.mjs", "test:next-update": "node scripts/test-next-15-update.mjs", "test:uri-fix": "node scripts/test-uri-fix.mjs", "test:clean-urls": "node scripts/test-clean-urls.mjs"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@google-ai/generativelanguage": "^2.5.0", "@google/generative-ai": "^0.21.0", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@langchain/community": "^0.3.49", "@langchain/core": "^0.3.64", "@langchain/google-genai": "^0.2.15", "@prisma/client": "^5.12.1", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@react-three/drei": "^10.3.0", "@react-three/fiber": "^9.1.2", "@react-three/postprocessing": "^3.0.4", "@splinetool/react-spline": "^4.0.0", "@splinetool/runtime": "^1.10.13", "@tailwindcss/typography": "^0.5.16", "@tavily/core": "^0.5.9", "@tiptap/extension-character-count": "^2.22.3", "@tiptap/extension-highlight": "^2.22.3", "@tiptap/extension-image": "^2.22.3", "@tiptap/extension-link": "^2.22.3", "@tiptap/extension-placeholder": "^2.22.3", "@tiptap/extension-subscript": "^2.22.3", "@tiptap/extension-superscript": "^2.22.3", "@tiptap/extension-table": "^2.22.3", "@tiptap/extension-table-cell": "^2.22.3", "@tiptap/extension-table-header": "^2.22.3", "@tiptap/extension-table-row": "^2.22.3", "@tiptap/extension-task-item": "^2.22.3", "@tiptap/extension-task-list": "^2.22.3", "@tiptap/extension-text-align": "^2.22.3", "@tiptap/extension-underline": "^2.22.3", "@tiptap/pm": "^2.14.0", "@tiptap/react": "^2.22.3", "@tiptap/starter-kit": "^2.22.3", "@tsparticles/engine": "^3.8.1", "@tsparticles/react": "^3.0.0", "@types/marked": "^5.0.2", "@types/node": "20.11.25", "@types/react": "18.2.64", "@types/react-dom": "18.2.21", "@types/react-syntax-highlighter": "^15.5.13", "@types/three": "^0.177.0", "autoprefixer": "10.4.18", "axios": "^1.6.8", "bcryptjs": "^3.0.2", "cheerio": "^1.0.0-rc.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "eslint": "8.57.0", "eslint-config-next": "14.1.3", "eventsource-parser": "^3.0.2", "framer-motion": "^11.0.12", "google-auth-library": "^9.15.1", "googleapis": "^134.0.0", "gsap": "^3.13.0", "jsonwebtoken": "^9.0.2", "kaibanjs": "^0.21.1", "langchain": "^0.3.28", "leva": "^0.10.0", "lottie-react": "^2.4.1", "lucide-react": "^0.356.0", "maath": "^0.10.8", "marked": "^15.0.12", "next": "^15.3.4", "next-auth": "^4.24.7", "openai": "^4.63.0", "postcss": "8.4.35", "process": "^0.11.10", "react": "18.2.0", "react-dom": "18.2.0", "react-icons": "^5.5.0", "react-markdown": "^9.1.0", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.15.4", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1", "sharp": "^0.34.3", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tailwindcss": "3.4.1", "tailwindcss-animate": "^1.0.7", "three": "^0.177.0", "tsparticles": "^3.8.1", "turndown": "^7.2.0", "typescript": "5.4.2", "uuid": "^9.0.1", "vaul": "^1.1.2", "youtubei.js": "^14.0.0", "zod": "^3.22.4"}, "devDependencies": {"@types/cheerio": "^0.22.35", "@types/turndown": "^5.0.5", "@types/uuid": "^9.0.8", "prisma": "^5.22.0", "tsx": "^4.20.3"}, "overrides": {"undici": "^5.28.4"}, "keywords": ["content writing", "seo", "ai", "saas", "content optimization", "article generator"], "author": "Content Team", "license": "MIT"}