/**
 * Enhanced Tavily Search Tool for KaibanJS Content Team
 * Provides comprehensive web research capabilities with intelligent result processing
 */

export class EnhancedTavilySearch {
  constructor() {
    // Parse API keys using array format
    const keysString = process.env.NEXT_PUBLIC_TAVILY_API_KEYS || process.env.TAVILY_API_KEYS || '';
    this.apiKeys = keysString
      .split(',')
      .map(key => key.trim())
      .filter(key => key.length > 0 && !key.includes('your_') && key !== 'undefined');
    
    this.currentKeyIndex = 0;
    this.isConfigured = this.apiKeys.length > 0;
    this.searchHistory = [];
    
    if (!this.isConfigured) {
      console.warn('⚠️ No Tavily API keys found in environment variables');
      console.log('💡 Expected format: TAVILY_API_KEYS=key1,key2,key3,key4');
    } else {
      console.log(`✅ Enhanced Tavily Search initialized with ${this.apiKeys.length} API keys`);
      this.logApiKeys();
    }
  }

  /**
   * Log available API keys (masked for security)
   */
  logApiKeys() {
    console.log('🔑 Available Tavily API Keys:');
    this.apiKeys.forEach((key, index) => {
      const maskedKey = `...${key.slice(-6)}`;
      const status = index === this.currentKeyIndex ? '← ACTIVE' : '';
      console.log(`   ${index + 1}. ${maskedKey} ${status}`);
    });
  }

  /**
   * Get current API key with rotation support
   */
  getCurrentApiKey() {
    if (this.apiKeys.length === 0) return null;
    return this.apiKeys[this.currentKeyIndex];
  }

  /**
   * Rotate to next API key
   */
  rotateApiKey() {
    if (this.apiKeys.length <= 1) {
      console.warn('⚠️ Only one API key available, cannot rotate');
      return;
    }
    
    const oldIndex = this.currentKeyIndex;
    this.currentKeyIndex = (this.currentKeyIndex + 1) % this.apiKeys.length;
    
    const oldKey = `...${this.apiKeys[oldIndex].slice(-6)}`;
    const newKey = `...${this.apiKeys[this.currentKeyIndex].slice(-6)}`;
    
    console.log(`🔄 Tavily key rotated: ${oldKey} → ${newKey} (${this.currentKeyIndex + 1}/${this.apiKeys.length})`);
  }

  /**
   * Perform enhanced web search with intelligent result processing
   * @param {string} query - Search query
   * @param {Object} options - Search options
   * @returns {Object} Enhanced search results
   */
  async search(query, options = {}) {
    const {
      maxResults = 10,
      searchDepth = 'advanced',
      includeAnswer = true,
      includeImages = false,
      includeRawContent = true,
      temporalFocus = 'current'
    } = options;

    if (!this.isConfigured) {
      console.warn('⚠️ Tavily not configured, returning mock results');
      return this.generateMockResults(query, maxResults);
    }

    try {
      console.log(`🔍 Tavily Enhanced Search: "${query}"`);
      
      // Get current API key
      const currentApiKey = this.getCurrentApiKey();
      if (!currentApiKey) {
        throw new Error('No API key available');
      }
      
      // Enhance query with temporal context
      const enhancedQuery = this.enhanceQueryWithTemporal(query, temporalFocus);
      
      const searchBody = {
        api_key: currentApiKey,
        query: enhancedQuery,
        max_results: Math.min(maxResults, 20),
        search_depth: searchDepth,
        include_answer: includeAnswer,
        include_raw_content: includeRawContent,
        include_images: includeImages
      };

      const response = await fetch('https://api.tavily.com/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(searchBody)
      });

      if (!response.ok) {
        throw new Error(`Tavily API error: ${response.status}`);
      }

      const data = await response.json();
      
      // Process and enhance results
      const processedResults = this.processResults(data, query);
      
      // Store in search history
      this.searchHistory.push({
        query: enhancedQuery,
        timestamp: Date.now(),
        resultCount: processedResults.results.length
      });

      console.log(`✅ Tavily search complete: ${processedResults.results.length} results`);
      
      return processedResults;

    } catch (error) {
      console.error('❌ Tavily search failed:', error);
      
      // Try rotating to next API key if available
      if (this.apiKeys.length > 1) {
        console.log('🔄 Trying with next API key...');
        this.rotateApiKey();
        
        // Retry once with new key
        try {
          const retryApiKey = this.getCurrentApiKey();
          const searchBody = {
            api_key: retryApiKey,
            query,
            max_results: Math.min(maxResults, 20),
            search_depth: searchDepth,
            include_answer: includeAnswer,
            include_raw_content: includeRawContent,
            include_images: includeImages
          };

          const retryResponse = await fetch('https://api.tavily.com/search', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(searchBody)
          });

          if (retryResponse.ok) {
            const retryData = await retryResponse.json();
            const processedResults = this.processResults(retryData, query);
            
            this.searchHistory.push({
              query,
              timestamp: Date.now(),
              resultCount: processedResults.results.length
            });

            console.log(`✅ Tavily retry successful: ${processedResults.results.length} results`);
            return processedResults;
          }
        } catch (retryError) {
          console.error('❌ Tavily retry also failed:', retryError);
        }
      }
      
      // Return mock results as fallback
      return this.generateMockResults(query, maxResults);
    }
  }

  /**
   * Enhanced query with temporal context
   */
  enhanceQueryWithTemporal(query, temporalFocus) {
    const currentYear = new Date().getFullYear();
    
    if (temporalFocus === 'current') {
      return `${query} ${currentYear} latest recent`;
    }
    
    return query;
  }

  /**
   * Process and enhance search results
   */
  processResults(data, originalQuery) {
    const results = (data.results || []).map((result, index) => ({
      title: result.title || '',
      url: result.url || '',
      content: result.content || '',
      snippet: result.content ? result.content.substring(0, 200) + '...' : '',
      score: result.score || 0.5,
      publishedDate: result.published_date || null,
      rawContent: result.raw_content || null,
      enhanced: true,
      rank: index + 1
    }));

    return {
      query: originalQuery,
      answer: data.answer || '',
      results,
      totalResults: results.length,
      images: data.images || [],
      followUpQuestions: data.follow_up_questions || [],
      searchMetrics: {
        responseTime: data.response_time || 0,
        enhanced: true
      }
    };
  }

  /**
   * Generate mock results when API is unavailable
   */
  generateMockResults(query, maxResults) {
    console.log(`🔄 Generating ${maxResults} mock results for: "${query}"`);
    
    const results = [];
    
    for (let i = 0; i < Math.min(maxResults, 5); i++) {
      results.push({
        title: `${query} - Resource ${i + 1}`,
        url: `https://example${i + 1}.com/resource`,
        content: `Comprehensive information about ${query}. This covers important aspects and provides valuable insights for your research.`,
        snippet: `Information about ${query}. This covers important aspects...`,
        score: 0.8 - (i * 0.1),
        publishedDate: new Date().toISOString(),
        rawContent: null,
        enhanced: false,
        rank: i + 1,
        mock: true
      });
    }

    return {
      query,
      answer: `Based on available information about ${query}, here are the key insights and relevant details.`,
      results,
      totalResults: results.length,
      images: [],
      followUpQuestions: [
        `What are the best practices for ${query}?`,
        `How to get started with ${query}?`,
        `What are the benefits of ${query}?`
      ],
      searchMetrics: {
        responseTime: 100,
        enhanced: false,
        mock: true
      }
    };
  }

  /**
   * Get search statistics
   */
  getSearchStats() {
    return {
      isConfigured: this.isConfigured,
      totalSearches: this.searchHistory.length,
      recentSearches: this.searchHistory.slice(-5)
    };
  }
}

// KaibanJS tool configuration
export const tavilySearchTool = {
  name: 'tavily_search',
  description: 'Enhanced web search tool using Tavily API for comprehensive research and information gathering.',
  
  async call(query, options = {}) {
    const searcher = new EnhancedTavilySearch();
    return await searcher.search(query, options);
  },
  
  // KaibanJS tool interface
  func: async (query, options = {}) => {
    const searcher = new EnhancedTavilySearch();
    const result = await searcher.search(query, options);
    return JSON.stringify(result, null, 2);
  }
};

// Export singleton instance
export const enhancedTavilySearch = new EnhancedTavilySearch();