/**
 * Invincible .1V Super Agent Team
 * Supreme content writing agent using KaibanJS framework
 */

import { Agent, Task, Team } from 'kaibanjs';
import { tavilySearchTool } from './tools/TavilySearchTool';
import { youtubeSearchTool } from './tools/YouTubeSearchTool';
import { captionExtractorTool } from './tools/CaptionExtractorTool';
import { contentAnalysisTool } from './tools/ContentAnalysisTool';

// ===== AGENTS =====

const researchAgent = new Agent({
  name: 'Research Specialist',
  role: 'Web Research Expert',
  goal: 'Conduct comprehensive web research to gather current, relevant information on any given topic',
  background: 'Expert in web research methodologies with access to advanced search tools. Specializes in finding authoritative sources, current trends, and comprehensive data on any topic.',
  tools: [tavilySearchTool]
});

const youtubeAgent = new Agent({
  name: 'YouTube Intelligence',
  role: 'Video Content Analyst',
  goal: 'Search, analyze, and extract insights from YouTube videos related to the topic',
  background: 'Specialist in video content analysis with expertise in finding relevant YouTube videos, extracting captions, and analyzing video content for insights and patterns.',
  tools: [youtubeSearchTool, captionExtractorTool]
});

const analysisAgent = new Agent({
  name: 'Competitive Analyst',
  role: 'Content Strategy Expert',
  goal: 'Analyze gathered content to identify competitive insights, gaps, and strategic opportunities',
  background: 'Expert in competitive analysis and content strategy. Specializes in identifying market gaps, competitive advantages, and content opportunities through comprehensive analysis.',
  tools: [contentAnalysisTool]
});

const writingAgent = new Agent({
  name: 'Supreme Writer',
  role: 'Content Creation Master',
  goal: 'Create superior, engaging content that outperforms competitors using all gathered research and insights',
  background: 'Master content creator with expertise in SEO, engagement, and authoritative writing. Combines research insights, competitive analysis, and video content to create superior articles that rank #1 and provide exceptional value.',
  tools: [contentAnalysisTool]
});

// ===== TASKS =====

const researchTask = new Task({
  title: 'Comprehensive Web Research',
  description: `Conduct thorough web research on the topic: "{topic}".
  
  Your research should:
  1. Search for the most current and authoritative information
  2. Find at least 8-10 high-quality sources
  3. Identify key trends, statistics, and expert opinions
  4. Gather comprehensive data covering all aspects of the topic
  5. Note any emerging developments or recent changes
  
  Focus on finding diverse perspectives and comprehensive coverage.`,
  expectedOutput: 'Detailed research report with sources, key findings, statistics, trends, and comprehensive information about the topic.',
  agent: researchAgent
});

const youtubeTask = new Task({
  title: 'YouTube Content Analysis',
  description: `Search and analyze YouTube content related to: "{topic}".
  
  Your analysis should:
  1. Find 3-5 most relevant and popular videos
  2. Extract captions/transcripts from top videos
  3. Identify common themes and approaches
  4. Note successful content patterns
  5. Extract key insights and data points mentioned
  
  Focus on understanding how the topic is presented in video format.`,
  expectedOutput: 'YouTube analysis report with video summaries, transcript insights, common themes, and content patterns.',
  agent: youtubeAgent
});

const competitiveAnalysisTask = new Task({
  title: 'Strategic Content Analysis',
  description: `Analyze all gathered research and YouTube content to identify strategic opportunities for: "{topic}".
  
  Your analysis should:
  1. Identify content gaps in existing materials
  2. Find competitive advantages and unique angles
  3. Determine what's missing from current content
  4. Identify opportunities to provide superior value
  5. Suggest content structure and key points to cover
  
  Focus on finding ways to create content that surpasses existing materials.`,
  expectedOutput: 'Strategic analysis report with content gaps, competitive insights, unique angles, and recommendations for superior content creation.',
  agent: analysisAgent
});

const contentCreationTask = new Task({
  title: 'Supreme Content Creation',
  description: `Create a comprehensive, superior article on: "{topic}" using all research, YouTube insights, and strategic analysis.
  
  Your content should:
  1. Incorporate all key insights from research and video analysis
  2. Address identified content gaps
  3. Provide unique value that competitors lack
  4. Include relevant statistics and expert insights
  5. Reference video content and external sources naturally
  6. Be structured for maximum engagement and SEO
  7. Include actionable takeaways and practical advice
  
  Create content that would rank #1 and become the definitive resource on this topic.
  Target word count: {wordCount} words.
  Tone: {tone}`,
  expectedOutput: 'Complete, superior article that incorporates all research findings, addresses content gaps, and provides exceptional value to readers.',
  agent: writingAgent
});

// ===== TEAM =====

export const invincibleTeam = new Team({
  name: 'Invincible .1V Super Agent',
  agents: [researchAgent, youtubeAgent, analysisAgent, writingAgent],
  tasks: [researchTask, youtubeTask, competitiveAnalysisTask, contentCreationTask],
  inputs: {
    topic: '',
    wordCount: 2000,
    tone: 'professional and engaging'
  },
  env: {
    OPENAI_API_KEY: process.env.OPENROUTER_API_KEY || process.env.OPENAI_API_KEY
  }
});

export default invincibleTeam;
