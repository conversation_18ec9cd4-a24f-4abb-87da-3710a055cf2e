/**
 * Caption Extractor Tool for KaibanJS
 * Extracts captions from YouTube videos using Supa Data API
 */

import { Tool } from "@langchain/core/tools";
import { z } from "zod";
import { YouTubeService } from '@/lib/youtube-service';

export class CaptionExtractorTool extends Tool {
  name = "caption_extractor";
  description = "Extract captions/transcripts from YouTube videos using video IDs. Returns timestamped captions and full transcript text.";
  
  schema = z.object({
    videoId: z.string().describe("The YouTube video ID to extract captions from"),
    language: z.string().optional().default('en').describe("Language code for captions (default: 'en')")
  });

  private youtubeService: YouTubeService;

  constructor() {
    super();
    this.youtubeService = new YouTubeService();
  }

  async _call(input: z.infer<typeof this.schema>): Promise<string> {
    try {
      console.log(`📝 Extracting captions for video: ${input.videoId}`);
      
      const captions = await this.youtubeService.extractCaptions(
        input.videoId, 
        input.language || 'en'
      );
      
      if (!captions || captions.length === 0) {
        return `No captions found for video ID: ${input.videoId}`;
      }

      // Combine captions into full transcript
      const fullTranscript = this.youtubeService.combineCaptions(captions);
      
      // Format results for agent consumption
      const formattedResults = {
        videoId: input.videoId,
        language: input.language || 'en',
        captionCount: captions.length,
        totalDuration: captions.length > 0 ? captions[captions.length - 1].start + captions[captions.length - 1].duration : 0,
        fullTranscript: fullTranscript,
        timestampedCaptions: captions.slice(0, 10).map(caption => ({
          text: caption.text,
          startTime: caption.start,
          duration: caption.duration,
          endTime: caption.start + caption.duration
        })), // Only include first 10 for brevity
        videoUrl: `https://www.youtube.com/watch?v=${input.videoId}`
      };

      console.log(`✅ Extracted ${captions.length} caption segments for video ${input.videoId}`);
      
      return JSON.stringify(formattedResults, null, 2);
      
    } catch (error) {
      console.error('❌ Caption extraction error:', error);
      return `Error extracting captions: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }
}

// Export singleton instance for KaibanJS
export const captionExtractorTool = new CaptionExtractorTool();
