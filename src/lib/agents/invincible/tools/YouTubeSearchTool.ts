/**
 * YouTube Search Tool for KaibanJS
 * Integrates with existing YouTube service using InnerTube
 */

import { Tool } from "@langchain/core/tools";
import { z } from "zod";
import { YouTubeService } from '@/lib/youtube-service';

export class YouTubeSearchTool extends Tool {
  name = "youtube_search";
  description = "Search for YouTube videos using InnerTube API. Returns video metadata including titles, descriptions, view counts, and video IDs.";
  
  schema = z.object({
    query: z.string().describe("The search query to find relevant YouTube videos"),
    maxResults: z.number().optional().default(5).describe("Maximum number of videos to return (default: 5)")
  });

  private youtubeService: YouTubeService;

  constructor() {
    super();
    this.youtubeService = new YouTubeService();
  }

  async _call(input: z.infer<typeof this.schema>): Promise<string> {
    try {
      console.log(`🎬 YouTube Search: "${input.query}"`);
      
      const searchResult = await this.youtubeService.searchVideos(
        input.query, 
        input.maxResults || 5
      );
      
      if (!searchResult || !searchResult.videos || searchResult.videos.length === 0) {
        return `No YouTube videos found for query: "${input.query}"`;
      }

      // Format results for agent consumption
      const formattedResults = {
        query: input.query,
        totalResults: searchResult.totalResults,
        videos: searchResult.videos.map((video, index) => ({
          rank: index + 1,
          id: video.id,
          title: video.title,
          description: video.description?.substring(0, 200) + '...' || 'No description',
          channel: video.channelTitle,
          viewCount: parseInt(video.viewCount) || 0,
          duration: video.duration,
          publishedAt: video.publishedAt,
          thumbnailUrl: video.thumbnailUrl,
          url: `https://www.youtube.com/watch?v=${video.id}`
        }))
      };

      console.log(`✅ Found ${formattedResults.videos.length} YouTube videos for "${input.query}"`);
      
      return JSON.stringify(formattedResults, null, 2);
      
    } catch (error) {
      console.error('❌ YouTube search error:', error);
      return `Error searching YouTube: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }
}

// Export singleton instance for KaibanJS
export const youtubeSearchTool = new YouTubeSearchTool();
