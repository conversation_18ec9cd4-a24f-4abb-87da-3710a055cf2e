/**
 * Tavily Search Tool for KaibanJS
 * Integrates with existing Tavily search functionality
 */

import { Tool } from "@langchain/core/tools";
import { z } from "zod";
import { enhancedTavilySearch } from '@/lib/tools/tavily-search.js';

export class TavilySearchTool extends Tool {
  name = "tavily_search";
  description = "Enhanced web search tool using Tavily API for comprehensive research and information gathering. Use this to find current, relevant information on any topic.";
  
  schema = z.object({
    query: z.string().describe("The search query to find relevant information"),
    maxResults: z.number().optional().default(10).describe("Maximum number of results to return (default: 10)"),
    searchDepth: z.enum(['basic', 'advanced']).optional().default('advanced').describe("Search depth - 'basic' for quick results, 'advanced' for comprehensive search"),
    temporalFocus: z.enum(['current', 'historical', 'auto']).optional().default('current').describe("Temporal focus for search results")
  });

  constructor() {
    super();
  }

  async _call(input: z.infer<typeof this.schema>): Promise<string> {
    try {
      console.log(`🔍 Tavily Search: "${input.query}"`);
      
      const searchOptions = {
        maxResults: input.maxResults || 10,
        searchDepth: input.searchDepth || 'advanced',
        includeAnswer: true,
        includeRawContent: true,
        temporalFocus: input.temporalFocus || 'current'
      };

      const results = await enhancedTavilySearch.search(input.query, searchOptions);
      
      if (!results || !results.results || results.results.length === 0) {
        return `No search results found for query: "${input.query}"`;
      }

      // Format results for agent consumption
      const formattedResults = {
        query: input.query,
        totalResults: results.results.length,
        answer: results.answer || '',
        sources: results.results.map((result: any, index: number) => ({
          rank: index + 1,
          title: result.title || 'No title',
          url: result.url || '',
          content: result.content || result.snippet || '',
          publishedDate: result.published_date || '',
          score: result.score || 0
        }))
      };

      console.log(`✅ Found ${formattedResults.totalResults} results for "${input.query}"`);
      
      return JSON.stringify(formattedResults, null, 2);
      
    } catch (error) {
      console.error('❌ Tavily search error:', error);
      return `Error performing search: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }
}

// Export singleton instance for KaibanJS
export const tavilySearchTool = new TavilySearchTool();
