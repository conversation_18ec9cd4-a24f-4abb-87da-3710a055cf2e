/**
 * Content Analysis Tool for KaibanJS
 * Uses AI models (Kimi K2 or Gemini) for content analysis
 */

import { Tool } from "@langchain/core/tools";
import { z } from "zod";
import { OpenRouterService } from '@/lib/openrouter';
import { GeminiService } from '@/lib/gemini';

export class ContentAnalysisTool extends Tool {
  name = "content_analysis";
  description = "Analyze content using AI models (Kimi K2 or Gemini) for competitive analysis, content gaps, and strategic insights.";
  
  schema = z.object({
    content: z.string().describe("The content to analyze (can be web content, video transcripts, etc.)"),
    analysisType: z.enum(['competitive', 'gaps', 'insights', 'structure', 'comprehensive']).describe("Type of analysis to perform"),
    topic: z.string().describe("The main topic or subject for context"),
    model: z.enum(['kimi-k2', 'gemini']).optional().default('kimi-k2').describe("AI model to use for analysis")
  });

  private openRouterService: OpenRouterService;
  private geminiService: GeminiService;

  constructor() {
    super();
    this.openRouterService = new OpenRouterService();
    this.geminiService = new GeminiService();
  }

  async _call(input: z.infer<typeof this.schema>): Promise<string> {
    try {
      console.log(`🧠 Content Analysis: ${input.analysisType} analysis for "${input.topic}"`);
      
      const analysisPrompts = {
        competitive: `Analyze the following content for competitive intelligence on "${input.topic}":

Content: ${input.content}

Provide:
1. Key competitors or alternatives mentioned
2. Strengths and weaknesses identified
3. Market positioning insights
4. Unique value propositions
5. Gaps in the competitive landscape`,

        gaps: `Identify content gaps and opportunities in the following content about "${input.topic}":

Content: ${input.content}

Provide:
1. Missing information or topics
2. Underexplored areas
3. Questions left unanswered
4. Opportunities for deeper coverage
5. Audience needs not addressed`,

        insights: `Extract key insights and takeaways from the following content about "${input.topic}":

Content: ${input.content}

Provide:
1. Main insights and key points
2. Data and statistics mentioned
3. Expert opinions or quotes
4. Trends and patterns identified
5. Actionable recommendations`,

        structure: `Analyze the structure and organization of the following content about "${input.topic}":

Content: ${input.content}

Provide:
1. Content structure and flow
2. Headings and sections used
3. Information hierarchy
4. Content format and style
5. Engagement techniques used`,

        comprehensive: `Perform a comprehensive analysis of the following content about "${input.topic}":

Content: ${input.content}

Provide detailed analysis covering:
1. Content quality and depth
2. Competitive positioning
3. Structural organization
4. Key insights and data
5. Content gaps and opportunities
6. Target audience alignment
7. Engagement and readability
8. Recommendations for improvement`
      };

      const prompt = analysisPrompts[input.analysisType];
      let analysisResult: string;

      if (input.model === 'gemini') {
        const result = await this.geminiService.generateContent(prompt, {
          temperature: 0.7,
          maxOutputTokens: 4000
        }, `Content Analysis - ${input.analysisType}`);
        analysisResult = result.response;
      } else {
        // Use Kimi K2 via OpenRouter
        const result = await this.openRouterService.generateContent(prompt, {
          temperature: 0.7,
          maxTokens: 4000
        });
        analysisResult = result.response;
      }

      const formattedResults = {
        topic: input.topic,
        analysisType: input.analysisType,
        model: input.model,
        contentLength: input.content.length,
        analysis: analysisResult,
        timestamp: new Date().toISOString()
      };

      console.log(`✅ Completed ${input.analysisType} analysis for "${input.topic}" using ${input.model}`);
      
      return JSON.stringify(formattedResults, null, 2);
      
    } catch (error) {
      console.error('❌ Content analysis error:', error);
      return `Error analyzing content: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }
}

// Export singleton instance for KaibanJS
export const contentAnalysisTool = new ContentAnalysisTool();
