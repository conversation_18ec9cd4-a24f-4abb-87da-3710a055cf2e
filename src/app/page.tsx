'use client'

import { useState, useEffect, Suspense } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import dynamic from 'next/dynamic'
import Link from 'next/link'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { 
  ArrowRight, 
  Sparkles, 
  Play, 
  Plus,
  Minus,
  ChevronDown,
  Globe,
  Zap,
  Shield,
  Cpu,
  Layers,
  Palette,
  Check,
  Star,
  Menu,
  X
} from 'lucide-react'
import { cn } from '@/lib/utils'

// Dynamically import Spline to avoid SSR issues
// TODO: Fix Spline import - currently has package export issues
// const Spline = dynamic(() => import('@splinetool/react-spline').then(mod => mod.default || mod.Spline || mod), {
//   ssr: false,
//   loading: () => <div className="w-full h-full bg-black/20 animate-pulse rounded-xl" />
// })

// Temporary Spline placeholder component
const Spline = ({ scene, className }: { scene: string; className?: string }) => {
  return (
    <div className={cn("relative w-full h-full overflow-hidden", className)}>
      <div className="absolute inset-0 bg-gradient-to-br from-violet-900/20 via-purple-900/10 to-indigo-900/20">
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center">
            <motion.div
              animate={{
                rotateY: [0, 360],
                scale: [1, 1.1, 1],
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                ease: "easeInOut"
              }}
              className="w-32 h-32 mx-auto mb-4"
            >
              <div className="w-full h-full bg-gradient-to-br from-violet-600 to-indigo-600 rounded-full opacity-20 blur-xl absolute inset-0" />
              <div className="w-full h-full bg-gradient-to-br from-violet-600 to-indigo-600 rounded-2xl transform rotate-45" />
            </motion.div>
            <p className="text-white/50 text-sm">3D Scene Loading...</p>
          </div>
        </div>
      </div>
    </div>
  )
}

// Modern Navigation with Glassmorphism
const Navigation = () => {
  const [isScrolled, setIsScrolled] = useState(false)
  const [mobileMenu, setMobileMenu] = useState(false)

  useEffect(() => {
    const handleScroll = () => setIsScrolled(window.scrollY > 20)
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  return (
    <motion.nav
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      className={cn(
        "fixed top-0 left-0 right-0 z-50 transition-all duration-300",
        isScrolled ? "py-4" : "py-6"
      )}
    >
      <div className="max-w-7xl mx-auto px-6">
        <div className={cn(
          "rounded-2xl backdrop-blur-xl border transition-all duration-300",
          isScrolled 
            ? "bg-black/40 border-white/10 shadow-2xl" 
            : "bg-white/5 border-white/5"
        )}>
          <div className="px-6 py-4">
            <div className="flex items-center justify-between">
              {/* Logo */}
              <Link href="/" className="flex items-center space-x-3 group">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-violet-600 to-indigo-600 rounded-xl blur-lg group-hover:blur-xl transition-all opacity-70" />
                  <div className="relative bg-black rounded-xl p-2.5 border border-white/20">
                    <Sparkles className="w-5 h-5 text-white" />
                  </div>
                </div>
                <span className="text-xl font-semibold text-white">AI Content Studio</span>
              </Link>

              {/* Desktop Menu */}
              <div className="hidden lg:flex items-center space-x-1">
                {['Products', 'Solutions', 'Resources', 'Pricing'].map((item) => (
                  <button
                    key={item}
                    className="px-4 py-2 text-gray-300 hover:text-white transition-colors rounded-lg hover:bg-white/5"
                  >
                    {item}
                  </button>
                ))}

              </div>

              {/* CTA Buttons */}
              <div className="hidden lg:flex items-center space-x-4">
                <Link href="/login">
                  <button className="px-4 py-2 text-gray-300 hover:text-white transition-colors">
                    Sign in
                  </button>
                </Link>
                <Link href="/login">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="relative group"
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-violet-600 to-indigo-600 rounded-lg blur-md group-hover:blur-lg transition-all opacity-70" />
                    <div className="relative bg-gradient-to-r from-violet-600 to-indigo-600 text-white px-6 py-2.5 rounded-lg font-medium">
                      Get Started Free
                    </div>
                  </motion.button>
                </Link>
              </div>

              {/* Mobile Menu Button */}
              <button
                onClick={() => setMobileMenu(!mobileMenu)}
                className="lg:hidden p-2 text-white"
              >
                {mobileMenu ? <X /> : <Menu />}
              </button>
            </div>
          </div>
        </div>
      </div>
    </motion.nav>
  )
}

// Hero Section with Spline 3D
const HeroSection = () => {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Gradients */}
      <div className="absolute inset-0 bg-black">
        <div className="absolute inset-0 bg-gradient-to-br from-violet-900/20 via-black to-indigo-900/20" />
        <motion.div
          animate={{
            opacity: [0.3, 0.5, 0.3],
            scale: [1, 1.2, 1],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute top-1/4 -left-1/4 w-[600px] h-[600px] bg-violet-500/30 rounded-full blur-[120px]"
        />
        <motion.div
          animate={{
            opacity: [0.3, 0.5, 0.3],
            scale: [1, 1.2, 1],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1
          }}
          className="absolute bottom-1/4 -right-1/4 w-[600px] h-[600px] bg-indigo-500/30 rounded-full blur-[120px]"
        />
      </div>

      {/* Spline 3D Scene */}
      <div className="absolute inset-0 z-0">
        <Suspense fallback={<div className="w-full h-full bg-black" />}>
          <Spline 
            scene="https://prod.spline.design/6Wq1Q7YGyM-iab9i/scene.splinecode"
            className="w-full h-full"
          />
        </Suspense>
      </div>

      {/* Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-6 text-center">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          {/* Badge */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2 }}
            className="inline-flex items-center space-x-2 px-4 py-2 rounded-full bg-white/10 backdrop-blur-md border border-white/20 mb-8"
          >
            <Sparkles className="w-4 h-4 text-violet-400" />
            <span className="text-sm text-gray-200">AI-Powered Content Creation</span>
            <ChevronDown className="w-4 h-4 text-gray-400" />
          </motion.div>

          {/* Headline */}
          <h1 className="text-6xl md:text-7xl lg:text-8xl font-bold mb-6 tracking-tight">
            <span className="block text-white">Content Creation</span>
            <span className="block bg-gradient-to-r from-violet-400 via-purple-400 to-indigo-400 bg-clip-text text-transparent">
              Redefined by AI
            </span>
          </h1>

          {/* Subheadline */}
          <p className="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto mb-10 leading-relaxed">
            Experience the future of content generation with advanced AI technology. 
            Create, iterate, and publish at the speed of thought.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/login">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="group relative px-8 py-4 rounded-xl font-semibold text-lg overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-violet-600 to-indigo-600 transition-all group-hover:scale-105" />
                <div className="relative flex items-center justify-center space-x-2 text-white">
                  <span>Start Creating</span>
                  <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                </div>
              </motion.button>
            </Link>

            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="group px-8 py-4 rounded-xl font-semibold text-lg bg-white/10 backdrop-blur-md border border-white/20 text-white hover:bg-white/20 transition-all"
            >
              <span className="flex items-center space-x-2">
                <Play className="w-5 h-5" />
                <span>Watch Demo</span>
              </span>
            </motion.button>
          </div>
        </motion.div>

        {/* Scroll Indicator */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1 }}
          className="absolute bottom-10 left-1/2 transform -translate-x-1/2"
        >
          <motion.div
            animate={{ y: [0, 10, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="w-6 h-10 rounded-full border-2 border-white/30 flex items-start justify-center p-1"
          >
            <div className="w-1 h-3 bg-white/50 rounded-full" />
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}

// Bento Grid Features Section
const BentoFeatures = () => {
  const features = [
    {
      title: "Real-time Collaboration",
      description: "Work with your team in real-time with live cursors and instant updates",
      icon: <Globe className="w-6 h-6" />,
      gradient: "from-blue-500 to-cyan-500",
      size: "col-span-1"
    },
    {
      title: "AI-Powered Research",
      description: "Deep research capabilities with advanced AI coordination",
      icon: <Layers className="w-6 h-6" />,
      gradient: "from-emerald-500 to-green-500",
      size: "col-span-1"
    },
    {
      title: "Smart Templates",
      description: "Pre-built templates for blogs, emails, social media, and more",
      icon: <Palette className="w-6 h-6" />,
      gradient: "from-orange-500 to-red-500",
      size: "col-span-1"
    },
    {
      title: "Enterprise Security",
      description: "Bank-level encryption and compliance for your sensitive data",
      icon: <Shield className="w-6 h-6" />,
      gradient: "from-pink-500 to-rose-500",
      size: "col-span-1"
    }
  ]

  return (
    <section className="py-32 px-6">
      <div className="max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-5xl md:text-6xl font-bold text-white mb-6">
            Everything you need to
            <span className="block bg-gradient-to-r from-violet-400 to-indigo-400 bg-clip-text text-transparent">
              create exceptional content
            </span>
          </h2>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto">
            Powerful features designed for modern content creators and teams
          </p>
        </motion.div>

        {/* Bento Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.1 }}
              className={cn(
                "group relative rounded-2xl p-8 bg-white/5 backdrop-blur-md border border-white/10 hover:bg-white/10 transition-all duration-300",
                feature.size
              )}
            >
              {/* Gradient Overlay */}
              <div className={cn(
                "absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-10 transition-opacity rounded-2xl",
                feature.gradient
              )} />

              {/* Content */}
              <div className="relative z-10">
                <div className={cn(
                  "inline-flex p-3 rounded-xl bg-gradient-to-br mb-4",
                  feature.gradient
                )}>
                  {feature.icon}
                </div>
                <h3 className="text-2xl font-semibold text-white mb-3">{feature.title}</h3>
                <p className="text-gray-400 leading-relaxed">{feature.description}</p>

                {feature.demo && (
                  <Link href={feature.link || "/login"}>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      className="mt-6 inline-flex items-center space-x-2 text-white font-medium"
                    >
                      <span>{feature.link ? 'Try Now' : 'Try Live Demo'}</span>
                      <ArrowRight className="w-4 h-4" />
                    </motion.button>
                  </Link>
                )}
              </div>

              {/* Hover Effect */}
              <motion.div
                className="absolute inset-0 rounded-2xl"
                initial={false}
                whileHover={{
                  boxShadow: "0 0 60px -15px rgba(139, 92, 246, 0.5)"
                }}
              />
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}

// Pricing Section with Glassmorphism
const PricingSection = () => {
  const plans = [
    {
      name: "Starter",
      price: "$29",
      description: "Perfect for individuals and freelancers",
      features: [
        "5,000 AI-generated words/month",
        "Access to all content types",
        "Basic templates library",
        "Email support",
        "Export to multiple formats"
      ],
      cta: "Start Free Trial",
      popular: false
    },
    {
      name: "Professional",
      price: "$79",
      description: "Ideal for growing teams and agencies",
      features: [
        "50,000 AI-generated words/month",
        "Advanced AI research tools",
        "Custom templates & workflows",
        "Priority support & training",
        "API access & integrations",
        "Team collaboration tools"
      ],
      cta: "Start Free Trial",
      popular: true
    },
    {
      name: "Enterprise",
      price: "Custom",
      description: "For large organizations with specific needs",
      features: [
        "Unlimited AI generation",
        "Custom AI model training",
        "Dedicated account manager",
        "SLA & security compliance",
        "White-label options",
        "Custom integrations"
      ],
      cta: "Contact Sales",
      popular: false
    }
  ]

  return (
    <section className="py-32 px-6 relative">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 2px 2px, white 1px, transparent 1px)`,
          backgroundSize: '40px 40px'
        }} />
      </div>

      <div className="relative max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-5xl md:text-6xl font-bold text-white mb-6">
            Simple, transparent pricing
          </h2>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto">
            Choose the perfect plan for your content needs. No hidden fees.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {plans.map((plan, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.1 }}
              className={cn(
                "relative rounded-2xl p-8 backdrop-blur-md border transition-all duration-300",
                plan.popular
                  ? "bg-white/10 border-violet-500/50 scale-105 shadow-2xl"
                  : "bg-white/5 border-white/10 hover:bg-white/10"
              )}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-gradient-to-r from-violet-600 to-indigo-600 text-white text-sm font-medium px-4 py-1 rounded-full">
                    Most Popular
                  </div>
                </div>
              )}

              <div className="mb-8">
                <h3 className="text-2xl font-semibold text-white mb-2">{plan.name}</h3>
                <p className="text-gray-400 mb-4">{plan.description}</p>
                <div className="flex items-baseline">
                  <span className="text-5xl font-bold text-white">{plan.price}</span>
                  {plan.price !== "Custom" && <span className="text-gray-400 ml-2">/month</span>}
                </div>
              </div>

              <ul className="space-y-4 mb-8">
                {plan.features.map((feature, idx) => (
                  <li key={idx} className="flex items-start space-x-3">
                    <Check className="w-5 h-5 text-violet-400 mt-0.5" />
                    <span className="text-gray-300">{feature}</span>
                  </li>
                ))}
              </ul>

              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className={cn(
                  "w-full py-3 rounded-lg font-semibold transition-all",
                  plan.popular
                    ? "bg-gradient-to-r from-violet-600 to-indigo-600 text-white"
                    : "bg-white/10 text-white hover:bg-white/20"
                )}
              >
                {plan.cta}
              </motion.button>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}

// Testimonials with Glassmorphism Cards
const TestimonialsSection = () => {
  const testimonials = [
    {
      name: "Sarah Chen",
      role: "Content Director",
      company: "TechFlow Inc.",
      content: "AI Content Studio has transformed how we create content. The AI technology is incredibly smart and the output quality is consistently exceptional.",
      rating: 5
    },
    {
      name: "Michael Roberts",
      role: "Marketing Manager",
      company: "Growth Labs",
      content: "The AI research capabilities are a game-changer. It's like having a team of expert researchers and writers working 24/7.",
      rating: 5
    },
    {
      name: "Emma Thompson",
      role: "Freelance Writer",
      company: "Independent",
      content: "I've 10x'd my output without sacrificing quality. The research capabilities alone are worth the investment.",
      rating: 5
    }
  ]

  return (
    <section className="py-32 px-6">
      <div className="max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-5xl md:text-6xl font-bold text-white mb-6">
            Trusted by content creators worldwide
          </h2>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto">
            See what our users have to say about their experience
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.1 }}
              className="group relative rounded-2xl p-8 bg-white/5 backdrop-blur-md border border-white/10 hover:bg-white/10 transition-all"
            >
              {/* Rating */}
              <div className="flex space-x-1 mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                ))}
              </div>

              {/* Content */}
              <p className="text-gray-300 mb-6 leading-relaxed">"{testimonial.content}"</p>

              {/* Author */}
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 rounded-full bg-gradient-to-br from-violet-500 to-indigo-500 flex items-center justify-center text-white font-semibold">
                  {testimonial.name.split(' ').map(n => n[0]).join('')}
                </div>
                <div>
                  <p className="text-white font-semibold">{testimonial.name}</p>
                  <p className="text-gray-400 text-sm">{testimonial.role} at {testimonial.company}</p>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}

// FAQ Section with Accordion
const FAQSection = () => {
  const [openIndex, setOpenIndex] = useState<number | null>(null)

  const faqs = [
    {
      question: "How does the AI content generation work?",
      answer: "Our AI system combines topic analysis, primary research, gap analysis, deep research, and content generation to create comprehensive, well-researched content."
    },
    {
      question: "Can I integrate Invincible with my existing tools?",
      answer: "Yes! We offer API access on Professional and Enterprise plans, allowing you to integrate our AI technology with your existing workflow. We also have native integrations with popular tools like WordPress, Medium, and more."
    },
    {
      question: "What makes Invincible different from other AI writing tools?",
      answer: "Our advanced AI approach and deep research capabilities set us apart. While other tools generate content from prompts, we perform comprehensive research, identify gaps, and create content that's both accurate and engaging."
    },
    {
      question: "Is my data secure?",
      answer: "Absolutely. We use bank-level encryption for all data transmission and storage. Your content and research data are never used to train our models, and we're compliant with GDPR, CCPA, and SOC 2."
    },
    {
      question: "Can I try Invincible before subscribing?",
      answer: "Yes! We offer a 14-day free trial with full access to all features. No credit card required. You can cancel anytime if it's not the right fit for you."
    }
  ]

  return (
    <section className="py-32 px-6">
      <div className="max-w-4xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-5xl md:text-6xl font-bold text-white mb-6">
            Frequently asked questions
          </h2>
          <p className="text-xl text-gray-400">
            Everything you need to know about Invincible
          </p>
        </motion.div>

        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.05 }}
            >
              <button
                onClick={() => setOpenIndex(openIndex === index ? null : index)}
                className="w-full text-left rounded-xl p-6 bg-white/5 backdrop-blur-md border border-white/10 hover:bg-white/10 transition-all"
              >
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-white pr-8">{faq.question}</h3>
                  <motion.div
                    animate={{ rotate: openIndex === index ? 45 : 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    {openIndex === index ? (
                      <Minus className="w-5 h-5 text-gray-400" />
                    ) : (
                      <Plus className="w-5 h-5 text-gray-400" />
                    )}
                  </motion.div>
                </div>

                <AnimatePresence>
                  {openIndex === index && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: "auto", opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.2 }}
                      className="overflow-hidden"
                    >
                      <p className="text-gray-400 mt-4 leading-relaxed">
                        {faq.answer}
                      </p>
                    </motion.div>
                  )}
                </AnimatePresence>
              </button>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}

// CTA Section
const CTASection = () => {
  return (
    <section className="py-32 px-6 relative overflow-hidden">
      {/* Background Gradient */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-r from-violet-600/20 to-indigo-600/20" />
      </div>

      <div className="relative max-w-4xl mx-auto text-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
        >
          <h2 className="text-5xl md:text-6xl font-bold text-white mb-6">
            Ready to transform your
            <span className="block bg-gradient-to-r from-violet-400 to-indigo-400 bg-clip-text text-transparent">
              content creation?
            </span>
          </h2>
          <p className="text-xl text-gray-300 mb-10 max-w-2xl mx-auto">
            Join thousands of creators who are already using Invincible to produce exceptional content at scale.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/login">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="group relative px-8 py-4 rounded-xl font-semibold text-lg"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-violet-600 to-indigo-600 rounded-xl blur-lg group-hover:blur-xl transition-all opacity-70" />
                <div className="relative bg-gradient-to-r from-violet-600 to-indigo-600 text-white px-8 py-4 rounded-xl">
                  Start Your Free Trial
                </div>
              </motion.button>
            </Link>

            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="px-8 py-4 rounded-xl font-semibold text-lg bg-white/10 backdrop-blur-md border border-white/20 text-white hover:bg-white/20 transition-all"
            >
              Schedule a Demo
            </motion.button>
          </div>

          <p className="text-gray-400 text-sm mt-8">
            No credit card required • 14-day free trial • Cancel anytime
          </p>
        </motion.div>
      </div>
    </section>
  )
}

// Footer
const Footer = () => {
  return (
    <footer className="py-20 px-6 border-t border-white/10">
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12">
          {/* Brand */}
          <div>
            <Link href="/" className="flex items-center space-x-3 mb-4">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-violet-600 to-indigo-600 rounded-xl blur-lg opacity-70" />
                <div className="relative bg-black rounded-xl p-2.5 border border-white/20">
                  <Sparkles className="w-5 h-5 text-white" />
                </div>
              </div>
              <span className="text-xl font-semibold text-white">Invincible</span>
            </Link>
            <p className="text-gray-400 text-sm">
              AI-powered content creation platform for modern creators and teams.
            </p>
          </div>

          {/* Links */}
          <div>
            <h4 className="text-white font-semibold mb-4">Product</h4>
            <ul className="space-y-2">
              {['Features', 'Pricing', 'API', 'Integrations'].map((item) => (
                <li key={item}>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors text-sm">
                    {item}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h4 className="text-white font-semibold mb-4">Company</h4>
            <ul className="space-y-2">
              {['About', 'Blog', 'Careers', 'Contact'].map((item) => (
                <li key={item}>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors text-sm">
                    {item}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h4 className="text-white font-semibold mb-4">Legal</h4>
            <ul className="space-y-2">
              {['Privacy', 'Terms', 'Security', 'Compliance'].map((item) => (
                <li key={item}>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors text-sm">
                    {item}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        </div>

        <div className="pt-8 border-t border-white/10 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-400 text-sm">
            © 2024 Invincible. All rights reserved.
          </p>
          <div className="flex space-x-6 mt-4 md:mt-0">
            {['Twitter', 'LinkedIn', 'GitHub'].map((social) => (
              <a key={social} href="#" className="text-gray-400 hover:text-white transition-colors text-sm">
                {social}
              </a>
            ))}
          </div>
        </div>
      </div>
    </footer>
  )
}

// Main Page Component
export default function HomePage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  // Redirect authenticated users to dashboard
  useEffect(() => {
    if (status === 'authenticated') {
      router.push('/dashboard')
    }
  }, [status, router])

  // Show loading while checking authentication
  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin w-12 h-12 border-2 border-violet-400 border-t-transparent rounded-full mx-auto"></div>
          <p className="text-gray-400">Loading...</p>
        </div>
      </div>
    )
  }

  // Don't render if authenticated (will redirect)
  if (status === 'authenticated') {
    return null
  }

  return (
    <main className="min-h-screen bg-black text-white overflow-x-hidden">
      <Navigation />
      <HeroSection />
      <BentoFeatures />
      <PricingSection />
      <TestimonialsSection />
      <FAQSection />
      <CTASection />
      <Footer />
    </main>
  )
} 