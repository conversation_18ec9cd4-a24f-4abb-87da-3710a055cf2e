import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { invincibleTeam } from '@/lib/agents/invincible/InvincibleTeam';

// GET endpoint to check Invincible agent status
export async function GET(req: NextRequest) {
  return NextResponse.json({
    agent: 'Invincible .1V',
    version: '1.0.0',
    status: 'ready',
    description: 'Supreme Content Writing Agent using KaibanJS Multi-Agent System',
    capabilities: [
      'Comprehensive web research using Tavily',
      'YouTube video search and caption extraction',
      'Competitive content analysis',
      'Superior content generation',
      'Multi-agent orchestration',
      'Real-time progress tracking'
    ],
    workflow: {
      agents: 4,
      description: 'Research → YouTube Analysis → Competitive Analysis → Content Creation'
    },
    features: [
      'KaibanJS-powered multi-agent system',
      'Tavily web search integration',
      'YouTube InnerTube video search',
      'Supa Data API caption extraction',
      'Kimi K2 and Gemini AI models',
      'Comprehensive competitive analysis',
      'Superior content generation',
      'Real-time progress tracking'
    ],
    integrations: {
      search: 'Tavily API for web research',
      youtube: 'InnerTube + Supa Data API',
      ai: 'Kimi K2 via OpenRouter + Gemini 2.5 Flash Lite',
      framework: 'KaibanJS multi-agent orchestration'
    }
  });
}

// POST endpoint to generate content
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { topic, wordCount = 2000, tone = 'professional and engaging' } = body;

    if (!topic || topic.trim().length === 0) {
      return NextResponse.json(
        { error: 'Topic is required' },
        { status: 400 }
      );
    }

    console.log('🚀 Invincible .1V Super Agent: Starting content generation');
    console.log(`📝 Topic: "${topic}"`);
    console.log(`📊 Word Count: ${wordCount}`);
    console.log(`🎭 Tone: ${tone}`);

    // Configure team inputs
    invincibleTeam.inputs = {
      topic: topic.trim(),
      wordCount: wordCount,
      tone: tone
    };

    // Start team execution
    console.log('🤖 Initializing KaibanJS team execution...');
    const startTime = Date.now();
    
    try {
      // Execute the team workflow
      const result = await invincibleTeam.start();
      
      const executionTime = Date.now() - startTime;
      console.log(`✅ Team execution completed in ${executionTime}ms`);

      // Extract the final content from the last task result
      const finalContent = result.tasks?.[result.tasks.length - 1]?.result || 'Content generation completed but no output received.';

      return NextResponse.json({
        success: true,
        content: finalContent,
        metadata: {
          topic,
          wordCount,
          tone,
          executionTime,
          tasksCompleted: result.tasks?.length || 0,
          agentsUsed: invincibleTeam.agents.length,
          timestamp: new Date().toISOString()
        },
        teamResult: result
      });

    } catch (teamError) {
      console.error('❌ KaibanJS team execution failed:', teamError);
      
      return NextResponse.json({
        success: false,
        error: 'Team execution failed',
        details: teamError instanceof Error ? teamError.message : 'Unknown team error',
        fallbackMessage: 'The Invincible .1V agent encountered an issue during execution. Please try again.'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('❌ Invincible agent error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
