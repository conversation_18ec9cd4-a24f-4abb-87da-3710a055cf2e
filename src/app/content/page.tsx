'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  FileText,
  Mail,
  Video,
  Crown,
  Clock,
  Trash2,
  Download,
  Copy,
  Search,
  Filter,
  RefreshCw,
  Grid3X3,
  List,
  X,
  Sparkles
} from 'lucide-react'
import Link from 'next/link'
import { useSession } from 'next-auth/react'

interface ContentItem {
  id: string
  type: string
  title: string
  content: string
  preview: string
  wordCount?: number
  tone?: string
  status: string
  createdAt: string
  updatedAt: string
  metadata?: any
  images?: Array<{
    id: string
    localPath: string
    headingText?: string
    width: number
    height: number
  }>
}

interface PaginationInfo {
  total: number
  limit: number
  offset: number
  hasMore: boolean
}

const contentTypeConfig = {
  blog: {
    icon: FileText,
    label: 'Blog Post',
    color: 'from-pink-500 to-rose-500',
    bgColor: 'bg-pink-500/10',
    borderColor: 'border-pink-500/20'
  },
  email: {
    icon: Mail,
    label: 'Email',
    color: 'from-emerald-500 to-teal-500',
    bgColor: 'bg-emerald-500/10',
    borderColor: 'border-emerald-500/20'
  },
  youtube_script: {
    icon: Video,
    label: 'YouTube Script',
    color: 'from-red-500 to-orange-500',
    bgColor: 'bg-red-500/10',
    borderColor: 'border-red-500/20'
  },
  invincible_research: {
    icon: Crown,
    label: 'Invincible V.1',
    color: 'from-violet-700 to-indigo-700',
    bgColor: 'bg-violet-700/10',
    borderColor: 'border-violet-700/20'
  },

  invincible: {
    icon: Crown,
    label: 'Invincible',
    color: 'from-violet-600 to-purple-600',
    bgColor: 'bg-violet-600/10',
    borderColor: 'border-violet-600/20'
  },
  video_alchemy: {
    icon: Video,
    label: 'Video Alchemy',
    color: 'from-blue-500 to-indigo-500',
    bgColor: 'bg-blue-500/10',
    borderColor: 'border-blue-500/20'
  },
  social_media: {
    icon: Sparkles,
    label: 'Social Media',
    color: 'from-cyan-500 to-blue-500',
    bgColor: 'bg-cyan-500/10',
    borderColor: 'border-cyan-500/20'
  }
}

export default function ContentPage() {
  const { data: session } = useSession()
  const [content, setContent] = useState<ContentItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [selectedType, setSelectedType] = useState<string>('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'title' | 'wordCount'>('newest')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [highlightId, setHighlightId] = useState<string | null>(null)
  const [pagination, setPagination] = useState<PaginationInfo>({
    total: 0,
    limit: 12,
    offset: 0,
    hasMore: false
  })
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [showFilters, setShowFilters] = useState(false)
  const [showFilterNotification, setShowFilterNotification] = useState(false)
  const [filterSource, setFilterSource] = useState('')
  const [isInitialized, setIsInitialized] = useState(false)

  // Initialize from URL parameters and then fetch content
  useEffect(() => {
    if (!session) return

    console.log('🔍 Processing URL parameters...') // Debug log
    const urlParams = new URLSearchParams(window.location.search)
    const typeParam = urlParams.get('type')
    const highlightParam = urlParams.get('highlight')
    console.log('📄 URL type parameter:', typeParam) // Debug log
    console.log('🎯 URL highlight parameter:', highlightParam) // Debug log
    
    if (typeParam && typeParam !== 'all') {
      console.log('✅ Setting selectedType to:', typeParam) // Debug log
      setSelectedType(typeParam)
      setShowFilters(true) // Show filters when coming from dashboard
      
      // Set the filter source for notification
      const contentConfig = contentTypeConfig[typeParam as keyof typeof contentTypeConfig]
      if (contentConfig) {
        setFilterSource(contentConfig.label)
        setShowFilterNotification(true)
        console.log('📢 Showing notification for:', contentConfig.label) // Debug log
        
        // Hide notification after 4 seconds
        setTimeout(() => {
          setShowFilterNotification(false)
        }, 4000)
        
        // Show visual feedback that filtering is active
        setTimeout(() => {
          const element = document.querySelector(`[data-filter="${typeParam}"]`)
          if (element) {
            element.scrollIntoView({ behavior: 'smooth', block: 'center' })
          }
        }, 500)
      }
    }

    if (highlightParam) {
      setHighlightId(highlightParam)
      console.log('🎯 Setting highlight ID:', highlightParam) // Debug log
      // Auto-remove highlight after 10 seconds
      setTimeout(() => {
        setHighlightId(null)
        console.log('🎯 Removing highlight') // Debug log
      }, 10000)
    }

    setIsInitialized(true)
    console.log('🚀 Initialization complete, selectedType:', typeParam || 'all') // Debug log
  }, [session])

  // Fetch content when initialized or when selectedType changes
  useEffect(() => {
    if (session && isInitialized) {
      fetchContent()
    }
  }, [selectedType, session, isInitialized])

  const fetchContent = async (offset: number = 0, append: boolean = false) => {
    try {
      if (offset === 0) setIsRefreshing(true)
      
      const params = new URLSearchParams({
        limit: pagination.limit.toString(),
        offset: offset.toString()
      })
      
      if (selectedType !== 'all') {
        params.append('type', selectedType)
        console.log('Fetching content with type filter:', selectedType) // Debug log
      }

      const url = `/api/content?${params}`
      console.log('Fetching from URL:', url) // Debug log
      
      const response = await fetch(url)
      if (response.ok) {
        const data = await response.json()
        console.log('API response:', data) // Debug log
        
        if (append) {
          setContent(prev => [...prev, ...(data.content || [])])
        } else {
          setContent(data.content || [])
        }
        
        setPagination(data.pagination || {
          total: 0,
          limit: 12,
          offset: 0,
          hasMore: false
        })
      } else {
        console.error('API response error:', response.status, response.statusText)
      }
    } catch (error) {
      console.error('Error fetching content:', error)
    } finally {
      setIsLoading(false)
      setIsRefreshing(false)
    }
  }

  const handleLoadMore = () => {
    if (pagination.hasMore) {
      fetchContent(pagination.offset + pagination.limit, true)
    }
  }

  const handleTypeFilter = (type: string) => {
    setSelectedType(type)
    setContent([])
    setPagination(prev => ({ ...prev, offset: 0 }))
  }

  const handleDelete = async (contentId: string) => {
    try {
      const response = await fetch(`/api/content?id=${contentId}`, {
        method: 'DELETE'
      })
      
      if (response.ok) {
        setContent(prev => prev.filter(item => item.id !== contentId))
        setPagination(prev => ({ ...prev, total: prev.total - 1 }))
      }
    } catch (error) {
      console.error('Error deleting content:', error)
    }
  }

  const handleCopy = async (content: string, contentType: string) => {
    try {
      let textToCopy = content
      
      // Handle social media content specially
      if (contentType === 'social_media') {
        try {
          const parsed = JSON.parse(content)
          if (typeof parsed === 'object' && parsed !== null) {
            // Format all platforms for copying
            const platformContents = Object.entries(parsed).map(([platform, data]: [string, any]) => {
              const platformName = platform.charAt(0).toUpperCase() + platform.slice(1)
              let platformContent = `=== ${platformName} ===\n${data.content || ''}`
              
              if (data.hashtags && data.hashtags.length > 0) {
                platformContent += `\n\nHashtags: ${data.hashtags.map((tag: string) => `#${tag}`).join(' ')}`
              }
              
              if (data.characterCount) {
                platformContent += `\nCharacters: ${data.characterCount}`
              }
              
              return platformContent
            })
            
            textToCopy = platformContents.join('\n\n' + '='.repeat(50) + '\n\n')
          }
        } catch (parseError) {
          console.error('Error parsing social media content:', parseError)
          // Fall back to original content
        }
      }
      
      await navigator.clipboard.writeText(textToCopy)
    } catch (error) {
      console.error('Error copying content:', error)
    }
  }

  const handleDownload = (item: ContentItem) => {
    let contentToDownload = item.content
    
    // Handle social media content specially
    if (item.type === 'social_media') {
      try {
        const parsed = JSON.parse(item.content)
        if (typeof parsed === 'object' && parsed !== null) {
          // Format all platforms for download
          const platformContents = Object.entries(parsed).map(([platform, data]: [string, any]) => {
            const platformName = platform.charAt(0).toUpperCase() + platform.slice(1)
            let platformContent = `=== ${platformName} ===\n${data.content || ''}`
            
            if (data.hashtags && data.hashtags.length > 0) {
              platformContent += `\n\nHashtags: ${data.hashtags.map((tag: string) => `#${tag}`).join(' ')}`
            }
            
            if (data.characterCount) {
              platformContent += `\nCharacters: ${data.characterCount}`
            }
            
            if (data.engagementScore) {
              platformContent += `\nEngagement Score: ${data.engagementScore}/100`
            }
            
            return platformContent
          })
          
          contentToDownload = platformContents.join('\n\n' + '='.repeat(50) + '\n\n')
        }
      } catch (parseError) {
        console.error('Error parsing social media content for download:', parseError)
        // Fall back to original content
      }
    }
    
    const element = document.createElement('a')
    const file = new Blob([contentToDownload], { type: 'text/plain' })
    element.href = URL.createObjectURL(file)
    element.download = `${item.title.replace(/[^a-z0-9]/gi, '_')}.txt`
    document.body.appendChild(element)
    element.click()
    document.body.removeChild(element)
  }

  // Helper function to ensure articles are stored and get proper URLs
  const getArticleViewUrl = async (item: ContentItem): Promise<string> => {
    try {
      // Handle YouTube scripts differently
      if (item.type === 'youtube_script') {
        return `/youtube-script-view?script=${encodeURIComponent(item.content)}&title=${encodeURIComponent(item.title)}`
      }

      // First check if this article already has a stored URL by using its ID
      if (item.id) {
        // Check if the article exists in the clean URL system
        const checkResponse = await fetch(`/api/articles/${item.id}`)
        if (checkResponse.ok) {
          return `/article-view/${item.id}`
        }
      }

      // If not found, store the article using the proper API
      const response = await fetch('/api/articles/store', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: item.title,
          content: item.content,
          type: item.type,
          metadata: item.metadata,
        }),
      })

      const data = await response.json()
      if (response.ok && data.success && data.url) {
        return data.url
      }

      // Fallback to legacy URL if storing fails
      console.warn('Failed to store article, using fallback URL')
      return `/article-view?article=${encodeURIComponent(item.content)}&title=${encodeURIComponent(item.title)}&type=${encodeURIComponent(item.type)}`
    } catch (error) {
      console.error('Error generating article URL:', error)
      // Fallback to legacy URL on error
      return `/article-view?article=${encodeURIComponent(item.content)}&title=${encodeURIComponent(item.title)}&type=${encodeURIComponent(item.type)}`
    }
  }

  const handleViewClick = async (item: ContentItem, event: React.MouseEvent) => {
    event.preventDefault()
    
    try {
      // Handle social media content differently
      if (item.type === 'social_media') {
        // Navigate to social media generator with pre-filled content for viewing
        const url = `/social-media-generator?view=${encodeURIComponent(item.id)}&content=${encodeURIComponent(item.content)}&title=${encodeURIComponent(item.title)}`
        window.location.href = url
        return
      }

      const url = await getArticleViewUrl(item)
      window.location.href = url
    } catch (error) {
      console.error('Error handling view click:', error)
      // Fallback to direct navigation with legacy URL
      if (item.type === 'youtube_script') {
        window.location.href = `/youtube-script-view?script=${encodeURIComponent(item.content)}&title=${encodeURIComponent(item.title)}`
      } else if (item.type === 'social_media') {
        window.location.href = `/social-media-generator?view=${encodeURIComponent(item.id)}&content=${encodeURIComponent(item.content)}&title=${encodeURIComponent(item.title)}`
      } else {
        window.location.href = `/article-view?article=${encodeURIComponent(item.content)}&title=${encodeURIComponent(item.title)}&type=${encodeURIComponent(item.type)}`
      }
    }
  }

  const filteredAndSortedContent = content
    .filter(item => 
      searchQuery === '' || 
      item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.content.toLowerCase().includes(searchQuery.toLowerCase())
    )
    .sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        case 'oldest':
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        case 'title':
          return a.title.localeCompare(b.title)
        case 'wordCount':
          return (b.wordCount || 0) - (a.wordCount || 0)
        default:
          return 0
      }
    })

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getContentConfig = (type: string) => {
    return contentTypeConfig[type as keyof typeof contentTypeConfig] || contentTypeConfig.blog
  }

  const ContentCard = ({ item, index }: { item: ContentItem; index: number }) => {
    const config = getContentConfig(item.type)
    const IconComponent = config.icon
    const isHighlighted = highlightId === item.id

    // Helper function to get social media preview
    const getSocialMediaPreview = (content: string) => {
      try {
        const parsed = JSON.parse(content)
        if (typeof parsed === 'object' && parsed !== null) {
          // Get the first platform's content for preview
          const platforms = Object.keys(parsed)
          if (platforms.length > 0) {
            const firstPlatform = parsed[platforms[0]]
            if (firstPlatform?.content) {
              // Limit preview to 150 characters
              const preview = firstPlatform.content.substring(0, 150)
              return preview.length < firstPlatform.content.length ? preview + '...' : preview
            }
          }
        }
        return content
      } catch {
        return content
      }
    }

    // Helper function to get platform count for social media
    const getSocialMediaPlatformCount = (content: string) => {
      try {
        const parsed = JSON.parse(content)
        if (typeof parsed === 'object' && parsed !== null) {
          return Object.keys(parsed).length
        }
        return 0
      } catch {
        return 0
      }
    }

    const displayPreview = item.type === 'social_media' 
      ? getSocialMediaPreview(item.content) 
      : item.preview

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{
          opacity: 1,
          y: 0,
          scale: isHighlighted ? 1.02 : 1,
          boxShadow: isHighlighted ? '0 20px 40px rgba(14, 165, 233, 0.3)' : undefined
        }}
        transition={{ delay: index * 0.05 }}
        className={`group relative backdrop-blur-xl border rounded-xl p-6 transition-all duration-300 ${
          isHighlighted
            ? 'bg-gradient-to-br from-sky-500/20 to-cyan-500/20 border-sky-400/50 shadow-2xl ring-2 ring-sky-400/30'
            : item.type === 'invincible_research'
            ? 'bg-gradient-to-br from-violet-900/20 to-indigo-900/20 border-violet-500/30 hover:border-violet-400/50 shadow-2xl'
            : `bg-white/5 ${config.borderColor} hover:bg-white/10 hover:border-white/30`
        }`}
      >
        {/* Glass reflection effect for Invincible content */}
        {item.type === 'invincible_research' && (
          <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent rounded-xl" />
        )}
        
        <div className="relative space-y-4">
          {/* Header */}
          <div className="flex items-start justify-between">
            <div className={`p-3 rounded-xl border ${
              item.type === 'invincible_research'
                ? 'bg-violet-700/30 border-violet-600/50 backdrop-blur-sm'
                : `${config.bgColor} ${config.borderColor}`
            }`}>
              <IconComponent className="w-6 h-6 text-white" />
            </div>

            <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={(e) => {
                  e.stopPropagation()
                  handleCopy(item.content, item.type)
                }}
                className="p-2 text-gray-400 hover:text-white rounded-lg hover:bg-white/10 transition-colors"
                title="Copy content"
              >
                <Copy className="w-4 h-4" />
              </motion.button>
              
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={(e) => {
                  e.stopPropagation()
                  handleDownload(item)
                }}
                className="p-2 text-gray-400 hover:text-white rounded-lg hover:bg-white/10 transition-colors"
                title="Download"
              >
                <Download className="w-4 h-4" />
              </motion.button>
              
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={(e) => {
                  e.stopPropagation()
                  handleDelete(item.id)
                }}
                className="p-2 text-gray-400 hover:text-red-400 rounded-lg hover:bg-red-500/10 transition-colors"
                title="Delete"
              >
                <Trash2 className="w-4 h-4" />
              </motion.button>
            </div>
          </div>

          {/* Content */}
          <div>
            <h3 className="text-white font-semibold text-lg mb-2 line-clamp-2">{item.title}</h3>
            <p className="text-gray-400 text-sm line-clamp-3 leading-relaxed">{displayPreview}</p>

            {/* Social Media Platform Info */}
            {item.type === 'social_media' && (
              <div className="mt-3 flex items-center space-x-2">
                <div className="flex items-center space-x-1">
                  <span className="text-xs text-gray-500 flex items-center space-x-1">
                    <span>📱</span>
                    <span>{getSocialMediaPlatformCount(item.content)} platform{getSocialMediaPlatformCount(item.content) !== 1 ? 's' : ''}</span>
                  </span>
                </div>
                {item.metadata?.platforms && (
                  <div className="flex -space-x-1">
                    {item.metadata.platforms.slice(0, 4).map((platform: string, index: number) => (
                      <div
                        key={platform}
                        className="w-6 h-6 rounded-full bg-gradient-to-r from-cyan-500 to-blue-500 border-2 border-white/20 flex items-center justify-center text-xs"
                        title={platform}
                      >
                        {platform === 'twitter' ? '𝕏' : 
                         platform === 'linkedin' ? '💼' : 
                         platform === 'facebook' ? '📘' : '📱'}
                      </div>
                    ))}
                    {item.metadata.platforms.length > 4 && (
                      <div className="w-6 h-6 rounded-full bg-gray-600 border-2 border-white/20 flex items-center justify-center text-xs text-white">
                        +{item.metadata.platforms.length - 4}
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}

            {/* Image Preview */}
            {item.images && item.images.length > 0 && (
              <div className="mt-3 flex items-center space-x-2">
                <div className="flex -space-x-2">
                  {item.images.slice(0, 3).map((image) => (
                    <div
                      key={image.id}
                      className="relative w-8 h-8 rounded-lg overflow-hidden border-2 border-white/20 bg-white/10"
                      title={image.headingText || 'Generated image'}
                    >
                      <img
                        src={image.localPath}
                        alt={image.headingText || 'Generated image'}
                        className="w-full h-full object-cover"
                        loading="lazy"
                      />
                    </div>
                  ))}
                </div>
                <span className="text-xs text-gray-500 flex items-center space-x-1">
                  <span>📸</span>
                  <span>{item.images.length} image{item.images.length !== 1 ? 's' : ''}</span>
                </span>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between pt-4 border-t border-white/10">
            <div className="flex items-center space-x-3 text-xs text-gray-500">
              <div className="flex items-center space-x-1">
                <Clock className="w-3 h-3" />
                <span>{formatDate(item.createdAt)}</span>
              </div>
              {item.wordCount && (
                <span>{item.wordCount.toLocaleString()} words</span>
              )}
            </div>

            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={(e) => handleViewClick(item, e)}
              className="px-3 py-1.5 bg-violet-600 hover:bg-violet-700 text-white text-sm rounded-lg transition-colors"
            >
              View
            </motion.button>
          </div>
        </div>
      </motion.div>
    )
  }

  if (!session) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-900 to-violet-900 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">Access Denied</h1>
          <p className="text-gray-400 mb-6">Please sign in to view your content.</p>
          <Link href="/login" className="px-6 py-3 bg-violet-600 hover:bg-violet-700 text-white rounded-lg transition-colors">
            Sign In
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-900 to-violet-900">
      <div className="container mx-auto px-6 py-8">
        {/* Filter Notification */}
        <AnimatePresence>
          {showFilterNotification && (
            <motion.div
              initial={{ opacity: 0, y: -50, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -50, scale: 0.95 }}
              className="mb-6 bg-gradient-to-r from-violet-600/20 to-indigo-600/20 backdrop-blur-xl border border-violet-500/30 rounded-xl p-4"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-violet-600/30 rounded-lg">
                    <Filter className="w-5 h-5 text-violet-300" />
                  </div>
                  <div>
                    <p className="text-white font-medium">Filtering by {filterSource}</p>
                    <p className="text-violet-200 text-sm">Showing content from your dashboard selection</p>
                  </div>
                </div>
                <button
                  onClick={() => setShowFilterNotification(false)}
                  className="p-1 text-violet-300 hover:text-white transition-colors"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Highlight Notification */}
        <AnimatePresence>
          {highlightId && (
            <motion.div
              initial={{ opacity: 0, y: -50, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -50, scale: 0.95 }}
              className="mb-6 bg-gradient-to-r from-sky-600/20 to-cyan-600/20 backdrop-blur-xl border border-sky-500/30 rounded-xl p-4"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-sky-600/30 rounded-lg">
                    <Sparkles className="w-5 h-5 text-sky-300" />
                  </div>
                  <div>
                    <p className="text-white font-medium">Recently Generated Content</p>
                    <p className="text-sky-200 text-sm">Your newly created content is highlighted below</p>
                  </div>
                </div>
                <button
                  onClick={() => setHighlightId(null)}
                  className="p-1 text-sky-300 hover:text-white transition-colors"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">My Content Library</h1>
          <p className="text-gray-400">Access and manage all your generated content</p>
        </div>

        {/* Controls */}
        <div className="mb-8 space-y-4">
          {/* Search and View Controls */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search content..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-violet-500/50 transition-colors"
              />
            </div>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="px-4 py-3 bg-white/5 hover:bg-white/10 border border-white/10 rounded-xl text-gray-400 hover:text-white transition-colors flex items-center space-x-2"
              >
                <Filter className="w-4 h-4" />
                <span>Filters</span>
              </button>
              
              <button
                onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
                className="p-3 bg-white/5 hover:bg-white/10 border border-white/10 rounded-xl text-gray-400 hover:text-white transition-colors"
              >
                {viewMode === 'grid' ? <List className="w-4 h-4" /> : <Grid3X3 className="w-4 h-4" />}
              </button>
              
              <button
                onClick={() => fetchContent()}
                disabled={isRefreshing}
                className="p-3 bg-white/5 hover:bg-white/10 border border-white/10 rounded-xl text-gray-400 hover:text-white transition-colors disabled:opacity-50"
              >
                <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              </button>
            </div>
          </div>

          {/* Filters */}
          <AnimatePresence>
            {showFilters && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-xl p-6"
              >
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Content Type Filter */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-3">Content Type</label>
                    <div className="flex flex-wrap gap-2">
                      <button
                        onClick={() => handleTypeFilter('all')}
                        className={`px-3 py-2 text-sm rounded-lg transition-colors ${
                          selectedType === 'all'
                            ? 'bg-violet-600 text-white'
                            : 'bg-white/5 text-gray-400 hover:bg-white/10 hover:text-white'
                        }`}
                      >
                        All ({pagination.total})
                      </button>
                      {Object.entries(contentTypeConfig).map(([type, config]) => (
                        <button
                          key={type}
                          data-filter={type}
                          onClick={() => handleTypeFilter(type)}
                          className={`px-3 py-2 text-sm rounded-lg transition-colors flex items-center space-x-1 ${
                            selectedType === type
                              ? 'bg-violet-600 text-white'
                              : 'bg-white/5 text-gray-400 hover:bg-white/10 hover:text-white'
                          }`}
                        >
                          <config.icon className="w-3 h-3" />
                          <span>{config.label}</span>
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Sort Options */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-3">Sort By</label>
                    <select
                      value={sortBy}
                      onChange={(e) => setSortBy(e.target.value as any)}
                      className="w-full px-3 py-2 bg-white/5 border border-white/10 rounded-lg text-white focus:outline-none focus:border-violet-500/50"
                    >
                      <option value="newest">Newest First</option>
                      <option value="oldest">Oldest First</option>
                      <option value="title">Title A-Z</option>
                      <option value="wordCount">Word Count (High to Low)</option>
                    </select>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Stats */}
        <div className="mb-6 grid grid-cols-1 sm:grid-cols-5 gap-4">
          <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-xl p-4">
            <div className="text-2xl font-bold text-white">{pagination.total}</div>
            <div className="text-sm text-gray-400">Total Content</div>
          </div>
          <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-xl p-4">
            <div className="text-2xl font-bold text-violet-400">
              {content.filter(item => item.type === 'invincible_research').length}
            </div>
            <div className="text-sm text-gray-400">Invincible V.1</div>
          </div>
          <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-xl p-4">
            <div className="text-2xl font-bold text-pink-400">
              {content.filter(item => item.type === 'blog').length}
            </div>
            <div className="text-sm text-gray-400">Blog Posts</div>
          </div>
          <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-xl p-4">
            <div className="text-2xl font-bold text-red-400">
              {content.filter(item => item.type === 'youtube_script').length}
            </div>
            <div className="text-sm text-gray-400">YouTube Scripts</div>
          </div>
          <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-xl p-4">
            <div className="text-2xl font-bold text-cyan-400">
              {content.filter(item => item.type === 'social_media').length}
            </div>
            <div className="text-sm text-gray-400">Social Media</div>
          </div>
        </div>

        {/* Content Grid/List */}
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div key={i} className="animate-pulse">
                <div className="h-64 bg-white/5 rounded-xl"></div>
              </div>
            ))}
          </div>
        ) : filteredAndSortedContent.length === 0 ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center py-20"
          >
            <FileText className="w-16 h-16 text-gray-600 mx-auto mb-6" />
            <h3 className="text-xl font-semibold text-white mb-2">No content found</h3>
            <p className="text-gray-400 mb-8">
              {searchQuery ? 'Try adjusting your search terms' : 'Start creating content to build your library'}
            </p>
            <Link href="/dashboard" className="px-6 py-3 bg-violet-600 hover:bg-violet-700 text-white rounded-lg transition-colors">
              Create Content
            </Link>
          </motion.div>
        ) : (
          <>
            <div className={`grid gap-6 ${
              viewMode === 'grid' 
                ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' 
                : 'grid-cols-1'
            }`}>
              {filteredAndSortedContent.map((item, index) => (
                <ContentCard key={item.id} item={item} index={index} />
              ))}
            </div>

            {/* Load More */}
            {pagination.hasMore && (
              <div className="text-center mt-12">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={handleLoadMore}
                  className="px-8 py-3 bg-white/5 hover:bg-white/10 border border-white/10 rounded-xl text-white transition-colors"
                >
                  Load More Content
                </motion.button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
} 