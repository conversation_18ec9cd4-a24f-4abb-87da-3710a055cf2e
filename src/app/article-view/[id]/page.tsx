'use client';

import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter, useParams } from 'next/navigation';
import { motion } from 'framer-motion';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { 
  ArrowLeft, 
  Copy, 
  Download, 
  Clock, 
  Sparkles,
  ExternalLink,
  Search,
  Globe,
  Check,
  Bookmark,
  Heart,
  FileText,
  Award,
  Shield,
  Calendar,
  Crown,
  Brain,
  BarChart,
  Eye,
  MessageCircle,
  Image as ImageIcon,
  ZoomIn
} from 'lucide-react';
import Link from 'next/link';
import { cn } from '@/lib/utils';

// Component for headings with associated images
interface HeadingWithImageProps {
  level: number;
  children: React.ReactNode;
  imagesByHeading: Record<string, StoredImage>;
}

function HeadingWithImage({ level, children, imagesByHeading }: HeadingWithImageProps) {
  const headingText = typeof children === 'string' ? children : children?.toString() || '';
  const headingId = headingText.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');
  const associatedImage = imagesByHeading[headingId];

  const HeadingTag = `h${level}` as keyof JSX.IntrinsicElements;
  
  const getHeadingClasses = (level: number) => {
    switch (level) {
      case 1: return 'text-4xl font-bold text-white mb-6';
      case 2: return 'text-3xl font-bold text-white mb-4 mt-8';
      case 3: return 'text-2xl font-bold text-white mb-3 mt-6';
      case 4: return 'text-xl font-bold text-white mb-3 mt-6';
      case 5: return 'text-lg font-bold text-white mb-2 mt-4';
      default: return 'text-base font-bold text-white mb-2 mt-4';
    }
  };

  return (
    <div className="heading-with-image-section">
      <HeadingTag className={getHeadingClasses(level)}>
        {children}
      </HeadingTag>
      
      {/* Display associated image if available */}
      {associatedImage && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6 rounded-xl overflow-hidden shadow-2xl border border-white/20"
        >
          <div className="relative group">
            <img
              src={associatedImage.localPath}
              alt={associatedImage.headingText || headingText}
              className="w-full h-auto object-cover transition-transform duration-300 group-hover:scale-105"
              style={{ maxHeight: '500px' }}
              loading="lazy"
            />
            
            {/* Image overlay with metadata */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <div className="absolute bottom-4 left-4 right-4">
                <div className="flex items-center justify-between text-white">
                  <div className="flex items-center space-x-2">
                    <ImageIcon className="w-4 h-4" />
                    <span className="text-sm font-medium">
                      {associatedImage.width} × {associatedImage.height}
                    </span>
                  </div>
                  <button
                    onClick={() => window.open(associatedImage.localPath, '_blank')}
                    className="flex items-center space-x-1 px-2 py-1 bg-black/50 rounded-lg backdrop-blur-sm hover:bg-black/70 transition-colors"
                    title="View full size"
                  >
                    <ZoomIn className="w-3 h-3" />
                    <span className="text-xs">Expand</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          {/* Image caption if available */}
          {associatedImage.headingText && (
            <div className="p-3 bg-white/5 backdrop-blur-sm border-t border-white/10">
              <p className="text-sm text-gray-300 italic">
                {associatedImage.headingText}
              </p>
            </div>
          )}
        </motion.div>
      )}
    </div>
  );
}

interface ContentScoring {
  seoScore: number;
  aeoScore: number;
  geoScore: number;
  readabilityScore: number;
  uniquenessScore: number;
  externalLinkingScore: number;
  overallScore: number;
  recommendations: string[];
}

interface StoredImage {
  id: string;
  localPath: string;
  originalUrl: string;
  filename: string;
  headingText?: string;
  width: number;
  height: number;
  size: number;
  createdAt: string;
}

interface Article {
  id: string;
  title: string;
  content: string;
  metadata?: any;
  type: string;
  wordCount?: number;
  tone?: string;
  language: string;
  createdAt: string;
  updatedAt: string;
  status: string;
  images?: StoredImage[];
}

export default function ArticleViewPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const params = useParams();
  const [article, setArticle] = useState<Article | null>(null);
  const [scores, setScores] = useState<ContentScoring | null>(null);
  const [copied, setCopied] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [readingProgress, setReadingProgress] = useState(0);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [isLiked, setIsLiked] = useState(false);
  const [imagesByHeading, setImagesByHeading] = useState<Record<string, StoredImage>>({});

  // Handle scroll progress
  useEffect(() => {
    const handleScroll = () => {
      const totalHeight = document.documentElement.scrollHeight - window.innerHeight;
      const progress = (window.scrollY / totalHeight) * 100;
      setReadingProgress(Math.min(progress, 100));
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Handle authentication redirect
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
    }
  }, [status, router]);

  // Fetch article by ID
  useEffect(() => {
    async function fetchArticle() {
      if (status === 'loading' || !params.id) return;
      
      if (status === 'unauthenticated') {
        router.push('/login');
        return;
      }

      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`/api/articles/${params.id}`);
        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || 'Failed to fetch article');
        }

        if (data.success && data.article) {
          // Check if this is a YouTube script and redirect if so
          if (data.article.type === 'youtube_script') {
            // Redirect to YouTube script viewer
            const youtubeUrl = `/youtube-script-view?script=${encodeURIComponent(data.article.content)}&title=${encodeURIComponent(data.article.title)}`;
            router.push(youtubeUrl);
            return;
          }

          setArticle(data.article);

          // Process images and map them to headings
          if (data.article.images && data.article.images.length > 0) {
            const imageMap: Record<string, StoredImage> = {};
            data.article.images.forEach((image: StoredImage) => {
              if (image.headingText) {
                const headingId = image.headingText.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');
                imageMap[headingId] = image;
              }
            });
            setImagesByHeading(imageMap);
          }

          // Generate mock scores if metadata contains scoring info
          if (data.article.metadata?.scores) {
            setScores(data.article.metadata.scores);
          } else {
            // Generate realistic mock scores based on article type
            const mockScores = generateMockScores(data.article.type, data.article.wordCount || 0);
            setScores(mockScores);
          }
        } else {
          throw new Error('Article not found');
        }
      } catch (error) {
        console.error('Error fetching article:', error);
        setError(error instanceof Error ? error.message : 'Failed to load article');
        
        // Redirect to dashboard after showing error
        setTimeout(() => {
          router.push('/dashboard');
        }, 3000);
      } finally {
        setLoading(false);
      }
    }

    fetchArticle();
  }, [params.id, status, router]);

  // Generate mock scores based on article type and quality
  const generateMockScores = (type: string, wordCount: number): ContentScoring => {
    // Base scores with some variation
    const baseScore = 75 + Math.random() * 20;
    
    return {
      seoScore: Math.min(95, baseScore + (type === 'blog' ? 10 : 5) + Math.random() * 10),
      aeoScore: Math.min(95, baseScore + Math.random() * 15),
      geoScore: Math.min(95, baseScore + Math.random() * 20),
      readabilityScore: Math.min(95, baseScore + (wordCount > 500 ? 5 : -5) + Math.random() * 10),
      uniquenessScore: Math.min(95, baseScore + Math.random() * 15),
      externalLinkingScore: Math.min(95, baseScore + Math.random() * 25),
      overallScore: Math.min(95, baseScore + Math.random() * 10),
      recommendations: [
        'Content shows excellent depth and research quality',
        'Strong competitive advantage over existing articles',
        'Human-like writing style detected',
        'Good use of statistics and insights'
      ]
    };
  };

  const copyToClipboard = () => {
    if (article) {
      navigator.clipboard.writeText(article.content);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const downloadArticle = () => {
    if (article) {
      const blob = new Blob([article.content], { type: 'text/markdown' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${article.title.replace(/\s+/g, '-').toLowerCase()}.md`;
      a.click();
      URL.revokeObjectURL(url);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 85) return 'from-emerald-500 to-green-500';
    if (score >= 70) return 'from-blue-500 to-cyan-500';
    if (score >= 50) return 'from-amber-500 to-orange-500';
    return 'from-red-500 to-rose-500';
  };

  const getScoreGrade = (score: number) => {
    if (score >= 90) return 'A+';
    if (score >= 85) return 'A';
    if (score >= 80) return 'A-';
    if (score >= 75) return 'B+';
    if (score >= 70) return 'B';
    if (score >= 65) return 'B-';
    if (score >= 60) return 'C+';
    if (score >= 55) return 'C';
    return 'D';
  };

  // Loading state
  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin w-12 h-12 border-2 border-violet-400 border-t-transparent rounded-full mx-auto"></div>
          <p className="text-gray-400">Loading article...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="p-3 bg-red-600/20 rounded-2xl mb-6 w-fit mx-auto">
            <FileText className="w-8 h-8 text-red-400" />
          </div>
          <h2 className="text-xl font-semibold text-white mb-2">Article Not Found</h2>
          <p className="text-gray-400 mb-6">{error}</p>
          <Link
            href="/dashboard"
            className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-violet-600 to-indigo-600 text-white rounded-xl hover:scale-105 transition-transform"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Go to Dashboard
          </Link>
        </div>
      </div>
    );
  }

  // Don't render if not authenticated
  if (status === 'unauthenticated' || !article) {
    return null;
  }

  const wordCount = article.wordCount || article.content.split(/\s+/).filter(word => word.length > 0).length;
  const readingTime = Math.ceil(wordCount / 200);

  return (
    <div className="min-h-screen bg-black">
      {/* Animated Background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-violet-900/10 via-black to-indigo-900/10" />
        
        {/* Animated orbs */}
        <motion.div
          animate={{
            x: [0, 100, 0],
            y: [0, -100, 0],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute top-1/4 left-1/4 w-[500px] h-[500px] bg-violet-500/10 rounded-full blur-[100px]"
        />
        <motion.div
          animate={{
            x: [0, -100, 0],
            y: [0, 100, 0],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute bottom-1/4 right-1/4 w-[600px] h-[600px] bg-indigo-500/10 rounded-full blur-[120px]"
        />
      </div>

      {/* Reading Progress Bar */}
      <motion.div 
        className="fixed top-0 left-0 h-1 bg-gradient-to-r from-violet-600 to-indigo-600 z-50"
        style={{ width: `${readingProgress}%` }}
      />

      {/* Modern Header */}
      <motion.header 
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="relative z-10 border-b border-white/10 backdrop-blur-xl bg-black/40"
      >
        <div className="max-w-7xl mx-auto px-6 py-6">
          <div className="flex items-center justify-between">
            {/* Left Side */}
            <div className="flex items-center space-x-6">
              <Link 
                href="/dashboard" 
                className="flex items-center space-x-3 text-gray-400 hover:text-white transition-colors group"
              >
                <ArrowLeft className="w-5 h-5 group-hover:-translate-x-1 transition-transform" />
                <span className="font-medium">Back to Dashboard</span>
              </Link>
              
              <div className="h-6 w-px bg-white/20" />
              
              <div className="flex items-center space-x-3">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-violet-600 to-indigo-600 rounded-xl blur-lg opacity-70" />
                  <div className="relative bg-black rounded-xl p-2.5 border border-white/20">
                    <Crown className="w-6 h-6 text-white" />
                  </div>
                </div>
                <div>
                  <h1 className="text-xl font-bold text-white">Superior Article</h1>
                  <p className="text-sm text-gray-400">
                    {article.type.charAt(0).toUpperCase() + article.type.slice(1)} Content
                  </p>
                </div>
              </div>
            </div>

            {/* Right Side Actions */}
            <div className="flex items-center space-x-4">
              <div className="hidden md:flex items-center space-x-6 text-sm text-gray-400">
                <div className="flex items-center space-x-2">
                  <Calendar className="w-4 h-4" />
                  <span>{new Date(article.createdAt).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Clock className="w-4 h-4" />
                  <span>{readingTime} min read</span>
                </div>
                <div className="flex items-center space-x-2">
                  <FileText className="w-4 h-4" />
                  <span>{wordCount.toLocaleString()} words</span>
                </div>
              </div>
              
              <div className="h-6 w-px bg-white/20" />
              
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => setIsBookmarked(!isBookmarked)}
                  className={cn(
                    "p-2.5 rounded-lg transition-colors",
                    isBookmarked ? 'bg-violet-600/20 text-violet-400' : 'text-gray-400 hover:text-white hover:bg-white/10'
                  )}
                >
                  <Bookmark className={`w-5 h-5 ${isBookmarked ? 'fill-current' : ''}`} />
                </button>
                
                <button
                  onClick={() => setIsLiked(!isLiked)}
                  className={cn(
                    "p-2.5 rounded-lg transition-colors",
                    isLiked ? 'bg-red-600/20 text-red-400' : 'text-gray-400 hover:text-white hover:bg-white/10'
                  )}
                >
                  <Heart className={`w-5 h-5 ${isLiked ? 'fill-current' : ''}`} />
                </button>
                
                <button
                  onClick={copyToClipboard}
                  className="flex items-center space-x-2 px-4 py-2.5 text-sm font-medium text-white bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg hover:bg-white/20 transition-all"
                >
                  {copied ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                  <span>{copied ? 'Copied' : 'Copy'}</span>
                </button>
                
                <motion.button
                  onClick={downloadArticle}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="flex items-center space-x-2 px-4 py-2.5 text-sm font-medium text-white bg-gradient-to-r from-violet-600 to-indigo-600 rounded-lg hover:shadow-lg transition-all"
                >
                  <Download className="w-4 h-4" />
                  <span>Download</span>
                </motion.button>
              </div>
            </div>
          </div>
        </div>
      </motion.header>

      {/* Main Content */}
      <main className="relative z-10 max-w-6xl mx-auto px-6 py-12">
        {/* Article Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center space-x-2 px-4 py-2 rounded-full bg-white/10 backdrop-blur-md border border-white/20 mb-8">
            <Sparkles className="w-4 h-4 text-violet-400" />
            <span className="text-sm text-gray-200">AI Generated</span>
            <Crown className="w-4 h-4 text-yellow-400" />
          </div>
          
          <div className="flex flex-wrap justify-center gap-3 mb-8">
            <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-violet-600/20 text-violet-300 border border-violet-500/30">
              <Sparkles className="w-4 h-4 mr-2" />
              AI Generated
            </span>
            <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-emerald-600/20 text-emerald-300 border border-emerald-500/30">
              <Shield className="w-4 h-4 mr-2" />
              SEO Optimized
            </span>
            <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-amber-600/20 text-amber-300 border border-amber-500/30">
              <Award className="w-4 h-4 mr-2" />
              Superior Quality
            </span>
          </div>
          
          <h1 className="text-4xl md:text-6xl font-bold text-white leading-tight mb-6">
            {article.title}
          </h1>
          
          <div className="flex items-center justify-center space-x-8 mb-8">
            <div className="flex items-center space-x-3">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-violet-600 to-indigo-600 rounded-full blur-md opacity-70" />
                <div className="relative w-12 h-12 bg-gradient-to-r from-violet-600 to-indigo-600 rounded-full flex items-center justify-center">
                  <Brain className="w-6 h-6 text-white" />
                </div>
              </div>
              <div className="text-left">
                <p className="text-sm font-medium text-white">Invincible AI</p>
                <p className="text-xs text-gray-400">Content Agent</p>
              </div>
            </div>
            
            <div className="h-8 w-px bg-white/20" />
            
            <div className="flex items-center space-x-6 text-sm text-gray-400">
              <div className="flex items-center space-x-2">
                <Clock className="w-4 h-4" />
                <span>{readingTime} min read</span>
              </div>
              <div className="flex items-center space-x-2">
                <FileText className="w-4 h-4" />
                <span>{wordCount.toLocaleString()} words</span>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Images Summary */}
        {article.images && article.images.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.15 }}
            className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6 mb-12"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-gradient-to-r from-emerald-600 to-teal-600 rounded-lg">
                  <ImageIcon className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-white">Enhanced with Images</h3>
                  <p className="text-sm text-gray-400">
                    {article.images.length} generated image{article.images.length !== 1 ? 's' : ''} included
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-emerald-600/20 text-emerald-300 border border-emerald-500/30">
                  <Sparkles className="w-3 h-3 mr-1" />
                  AI Generated
                </span>
              </div>
            </div>
          </motion.div>
        )}

        {/* Quality Scores */}
        {scores && (
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-8 mb-12"
          >
            <div className="flex items-center justify-between mb-8">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-gradient-to-r from-violet-600 to-indigo-600 rounded-lg">
                  <BarChart className="w-5 h-5 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-white">Content Quality Analysis</h3>
              </div>
              <div className="flex items-center space-x-3">
                <span className="text-sm text-gray-400">Overall Grade:</span>
                <span className={`text-3xl font-bold bg-gradient-to-r ${getScoreColor(scores.overallScore)} bg-clip-text text-transparent`}>
                  {getScoreGrade(scores.overallScore)}
                </span>
              </div>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8">
              {[
                { label: 'SEO Score', score: scores.seoScore, icon: Search, color: 'violet' },
                { label: 'AEO Score', score: scores.aeoScore, icon: MessageCircle, color: 'blue' },
                { label: 'GEO Score', score: scores.geoScore, icon: Globe, color: 'emerald' },
                { label: 'Readability', score: scores.readabilityScore, icon: Eye, color: 'amber' },
                { label: 'Uniqueness', score: scores.uniquenessScore, icon: Sparkles, color: 'pink' },
                { label: 'External Links', score: scores.externalLinkingScore, icon: ExternalLink, color: 'cyan' }
              ].map((metric, idx) => (
                <motion.div
                  key={metric.label}
                  initial={{ scale: 0, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ delay: 0.05 * idx }}
                  className="text-center p-4 rounded-xl bg-white/5 border border-white/10 hover:bg-white/10 transition-all"
                >
                  <div className="relative mb-4">
                    <svg className="w-24 h-24 mx-auto transform -rotate-90">
                      <circle
                        cx="48"
                        cy="48"
                        r="40"
                        stroke="currentColor"
                        strokeWidth="6"
                        fill="none"
                        className="text-white/20"
                      />
                      <circle
                        cx="48"
                        cy="48"
                        r="40"
                        stroke={`url(#gradient-${idx})`}
                        strokeWidth="6"
                        fill="none"
                        strokeDasharray={`${(metric.score / 100) * 251.33} 251.33`}
                        strokeLinecap="round"
                        className="transition-all duration-1000"
                      />
                      <defs>
                        <linearGradient id={`gradient-${idx}`}>
                          <stop offset="0%" stopColor={`var(--${metric.color}-500)`} />
                          <stop offset="100%" stopColor={`var(--${metric.color}-400)`} />
                        </linearGradient>
                      </defs>
                    </svg>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="text-center">
                        <p className="text-2xl font-bold text-white">{Math.round(metric.score)}</p>
                        <p className="text-xs text-gray-400">/ 100</p>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className={cn(
                      "inline-flex p-2 rounded-lg",
                      metric.color === 'violet' && "bg-violet-600/20",
                      metric.color === 'blue' && "bg-blue-600/20",
                      metric.color === 'emerald' && "bg-emerald-600/20",
                      metric.color === 'amber' && "bg-amber-600/20",
                      metric.color === 'pink' && "bg-pink-600/20",
                      metric.color === 'cyan' && "bg-cyan-600/20"
                    )}>
                      <metric.icon className="w-5 h-5 text-white" />
                    </div>
                    <h4 className="text-sm font-semibold text-white">{metric.label}</h4>
                    <p className="text-xs text-gray-400">
                      {metric.score >= 85 ? 'Excellent' : metric.score >= 70 ? 'Good' : metric.score >= 50 ? 'Fair' : 'Needs Work'}
                    </p>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}

        {/* Article Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-8"
        >
          <div className="prose prose-invert prose-lg max-w-none">
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              components={{
                h1: ({ children }) => (
                  <HeadingWithImage level={1} imagesByHeading={imagesByHeading}>
                    {children}
                  </HeadingWithImage>
                ),
                h2: ({ children }) => (
                  <HeadingWithImage level={2} imagesByHeading={imagesByHeading}>
                    {children}
                  </HeadingWithImage>
                ),
                h3: ({ children }) => (
                  <HeadingWithImage level={3} imagesByHeading={imagesByHeading}>
                    {children}
                  </HeadingWithImage>
                ),
                h4: ({ children }) => (
                  <HeadingWithImage level={4} imagesByHeading={imagesByHeading}>
                    {children}
                  </HeadingWithImage>
                ),
                h5: ({ children }) => (
                  <HeadingWithImage level={5} imagesByHeading={imagesByHeading}>
                    {children}
                  </HeadingWithImage>
                ),
                h6: ({ children }) => (
                  <HeadingWithImage level={6} imagesByHeading={imagesByHeading}>
                    {children}
                  </HeadingWithImage>
                ),
                p: ({ children }) => <p className="text-gray-300 mb-4 leading-relaxed">{children}</p>,
                a: ({ href, children }) => (
                  <a href={href} className="text-violet-400 hover:text-violet-300 underline" target="_blank" rel="noopener noreferrer">
                    {children}
                  </a>
                ),
                ul: ({ children }) => (
                  <ul className="text-gray-300 mb-6 ml-6 space-y-3 list-disc list-outside">
                    {children}
                  </ul>
                ),
                ol: ({ children }) => (
                  <ol className="text-gray-300 mb-6 ml-6 space-y-3 list-decimal list-outside">
                    {children}
                  </ol>
                ),
                li: ({ children }) => (
                  <li className="text-gray-300 leading-relaxed">
                    {children}
                  </li>
                ),
                blockquote: ({ children }) => (
                  <blockquote className="border-l-4 border-violet-500 pl-4 italic text-gray-400 my-6">
                    {children}
                  </blockquote>
                ),
                code: ({ children }) => (
                  <code className="bg-white/10 px-2 py-1 rounded text-violet-300 font-mono text-sm">
                    {children}
                  </code>
                ),
                table: ({ children }) => (
                  <div className="overflow-x-auto my-8 rounded-2xl shadow-2xl border border-white/20 bg-gradient-to-br from-slate-800/50 to-slate-900/50 backdrop-blur-sm">
                    <table className="min-w-full border-collapse">
                      {children}
                    </table>
                  </div>
                ),
                thead: ({ children }) => (
                  <thead className="bg-gradient-to-r from-blue-600/20 to-purple-600/20 border-b border-white/20">
                    {children}
                  </thead>
                ),
                tbody: ({ children }) => (
                  <tbody className="divide-y divide-white/10">
                    {children}
                  </tbody>
                ),
                tr: ({ children }) => (
                  <tr className="hover:bg-white/5 transition-all duration-200">
                    {children}
                  </tr>
                ),
                th: ({ children }) => (
                  <th className="px-6 py-4 text-left font-semibold text-white border-r border-white/10 last:border-r-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 relative">
                    <div className="absolute top-0 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500"></div>
                    {children}
                  </th>
                ),
                td: ({ children }) => (
                  <td className="px-6 py-4 text-gray-200 border-r border-white/10 last:border-r-0 font-medium">
                    {children}
                  </td>
                ),
              }}
            >
              {article.content}
            </ReactMarkdown>
          </div>
        </motion.div>
      </main>
    </div>
  );
} 