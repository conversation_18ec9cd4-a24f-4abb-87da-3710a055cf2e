'use client';

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { InvincibleProgress } from '@/components/InvincibleProgress';

export default function InvinciblePage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [topic, setTopic] = useState('');
  const [wordCount, setWordCount] = useState(2000);
  const [tone, setTone] = useState('professional and engaging');
  const [isGenerating, setIsGenerating] = useState(false);
  const [progress, setProgress] = useState<any>(null);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // Redirect if not authenticated
  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <div className="text-white text-xl">Loading...</div>
      </div>
    );
  }

  if (status === 'unauthenticated') {
    router.push('/auth/signin');
    return null;
  }

  const handleGenerate = async () => {
    if (!topic.trim()) {
      setError('Please enter a topic');
      return;
    }

    setIsGenerating(true);
    setError(null);
    setResult(null);
    setProgress({ phase: 'initializing', message: 'Initializing Invincible .1V Super Agent...' });

    try {
      const response = await fetch('/api/invincible', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          topic: topic.trim(),
          wordCount,
          tone
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to generate content');
      }

      if (data.success) {
        setResult(data);
        setProgress({ phase: 'completed', message: 'Content generation completed!' });
      } else {
        throw new Error(data.error || 'Content generation failed');
      }

    } catch (err) {
      console.error('Generation error:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
      setProgress(null);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleViewArticle = () => {
    if (result?.content) {
      // Create a temporary article object for the article view
      const articleData = {
        title: topic,
        content: result.content,
        type: 'invincible_generated',
        metadata: result.metadata
      };
      
      // Store in sessionStorage for the article view
      sessionStorage.setItem('invincible_article', JSON.stringify(articleData));
      
      // Navigate to article view
      router.push('/article-view/invincible-temp');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center space-x-3 mb-4">
            <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
              <span className="text-3xl font-bold text-white">⚡</span>
            </div>
            <div>
              <h1 className="text-4xl font-bold text-white">Invincible .1V</h1>
              <p className="text-blue-200">Supreme Content Writing Agent</p>
            </div>
          </div>
          <p className="text-gray-300 max-w-2xl mx-auto">
            Powered by KaibanJS multi-agent system with Tavily search, YouTube analysis, 
            and AI-driven content creation using Kimi K2 and Gemini models.
          </p>
        </div>

        {/* Input Form */}
        <div className="max-w-4xl mx-auto">
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 mb-8">
            <div className="space-y-6">
              {/* Topic Input */}
              <div>
                <label className="block text-white font-medium mb-2">
                  Topic *
                </label>
                <input
                  type="text"
                  value={topic}
                  onChange={(e) => setTopic(e.target.value)}
                  placeholder="Enter your topic (e.g., 'Best AI tools for content creation')"
                  className="w-full px-4 py-3 rounded-lg bg-white/20 border border-white/30 text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-yellow-400"
                  disabled={isGenerating}
                />
              </div>

              {/* Word Count */}
              <div>
                <label className="block text-white font-medium mb-2">
                  Word Count
                </label>
                <select
                  value={wordCount}
                  onChange={(e) => setWordCount(parseInt(e.target.value))}
                  className="w-full px-4 py-3 rounded-lg bg-white/20 border border-white/30 text-white focus:outline-none focus:ring-2 focus:ring-yellow-400"
                  disabled={isGenerating}
                >
                  <option value={1000}>1,000 words</option>
                  <option value={1500}>1,500 words</option>
                  <option value={2000}>2,000 words</option>
                  <option value={2500}>2,500 words</option>
                  <option value={3000}>3,000 words</option>
                </select>
              </div>

              {/* Tone */}
              <div>
                <label className="block text-white font-medium mb-2">
                  Tone
                </label>
                <select
                  value={tone}
                  onChange={(e) => setTone(e.target.value)}
                  className="w-full px-4 py-3 rounded-lg bg-white/20 border border-white/30 text-white focus:outline-none focus:ring-2 focus:ring-yellow-400"
                  disabled={isGenerating}
                >
                  <option value="professional and engaging">Professional & Engaging</option>
                  <option value="casual and friendly">Casual & Friendly</option>
                  <option value="authoritative and expert">Authoritative & Expert</option>
                  <option value="conversational and approachable">Conversational & Approachable</option>
                </select>
              </div>

              {/* Generate Button */}
              <button
                onClick={handleGenerate}
                disabled={isGenerating || !topic.trim()}
                className="w-full bg-gradient-to-r from-yellow-400 to-orange-500 text-white font-bold py-4 px-8 rounded-lg hover:from-yellow-500 hover:to-orange-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105"
              >
                {isGenerating ? (
                  <div className="flex items-center justify-center space-x-2">
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Generating Supreme Content...</span>
                  </div>
                ) : (
                  <div className="flex items-center justify-center space-x-2">
                    <span>⚡</span>
                    <span>Unleash Invincible .1V</span>
                  </div>
                )}
              </button>
            </div>
          </div>

          {/* Progress Display */}
          {(isGenerating || progress) && (
            <InvincibleProgress 
              progress={progress} 
              isGenerating={isGenerating}
            />
          )}

          {/* Error Display */}
          {error && (
            <div className="bg-red-500/20 border border-red-500/50 rounded-lg p-4 mb-8">
              <div className="flex items-center space-x-2">
                <span className="text-red-400">❌</span>
                <span className="text-red-300">{error}</span>
              </div>
            </div>
          )}

          {/* Results Display */}
          {result && (
            <div className="bg-green-500/20 border border-green-500/50 rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <span className="text-green-400">✅</span>
                  <span className="text-green-300 font-medium">Content Generated Successfully!</span>
                </div>
                <button
                  onClick={handleViewArticle}
                  className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  View Article
                </button>
              </div>
              
              {result.metadata && (
                <div className="text-sm text-gray-300 space-y-1">
                  <p>Execution Time: {(result.metadata.executionTime / 1000).toFixed(2)}s</p>
                  <p>Tasks Completed: {result.metadata.tasksCompleted}</p>
                  <p>Agents Used: {result.metadata.agentsUsed}</p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
