'use client';

import { useEffect, useState } from 'react';

interface ProgressProps {
  progress: any;
  isGenerating: boolean;
}

interface ProgressStep {
  id: string;
  name: string;
  description: string;
  icon: string;
  status: 'pending' | 'active' | 'completed';
}

export function InvincibleProgress({ progress, isGenerating }: ProgressProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [steps, setSteps] = useState<ProgressStep[]>([
    {
      id: 'research',
      name: 'Web Research',
      description: 'Gathering comprehensive information using Tavily search',
      icon: '🔍',
      status: 'pending'
    },
    {
      id: 'youtube',
      name: 'YouTube Analysis',
      description: 'Searching videos and extracting captions',
      icon: '🎬',
      status: 'pending'
    },
    {
      id: 'analysis',
      name: 'Competitive Analysis',
      description: 'Analyzing content gaps and opportunities',
      icon: '🧠',
      status: 'pending'
    },
    {
      id: 'writing',
      name: 'Content Creation',
      description: 'Generating superior content',
      icon: '✍️',
      status: 'pending'
    }
  ]);

  const [sources, setSources] = useState<string[]>([]);
  const [videos, setVideos] = useState<string[]>([]);
  const [queries, setQueries] = useState<string[]>([]);

  useEffect(() => {
    if (!isGenerating) return;

    // Simulate progress updates
    const progressInterval = setInterval(() => {
      setCurrentStep(prev => {
        const next = Math.min(prev + 1, steps.length - 1);
        
        // Update step statuses
        setSteps(prevSteps => prevSteps.map((step, index) => ({
          ...step,
          status: index < next ? 'completed' : index === next ? 'active' : 'pending'
        })));

        // Simulate data sources being discovered
        if (next === 0) {
          setSources(prev => [...prev, 'TechCrunch', 'Wired Magazine', 'MIT Technology Review']);
          setQueries(prev => [...prev, 'Latest AI trends 2025', 'Best AI tools comparison']);
        } else if (next === 1) {
          setVideos(prev => [...prev, 'AI Explained by Expert', 'Top 10 AI Tools 2025']);
          setQueries(prev => [...prev, 'AI tutorial videos', 'AI tools demonstration']);
        } else if (next === 2) {
          setQueries(prev => [...prev, 'Content gap analysis', 'Competitive positioning']);
        }

        return next;
      });
    }, 3000); // Update every 3 seconds

    return () => clearInterval(progressInterval);
  }, [isGenerating, steps.length]);

  if (!isGenerating && !progress) return null;

  return (
    <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 mb-8">
      <div className="text-center mb-8">
        <h3 className="text-2xl font-bold text-white mb-2">
          Invincible .1V in Action
        </h3>
        <p className="text-gray-300">
          Multi-agent system working to create superior content
        </p>
      </div>

      {/* Progress Steps */}
      <div className="space-y-6 mb-8">
        {steps.map((step, index) => (
          <div key={step.id} className="flex items-center space-x-4">
            {/* Step Icon */}
            <div className={`w-12 h-12 rounded-full flex items-center justify-center text-xl ${
              step.status === 'completed' 
                ? 'bg-green-500 text-white' 
                : step.status === 'active'
                ? 'bg-yellow-500 text-white animate-pulse'
                : 'bg-gray-600 text-gray-400'
            }`}>
              {step.status === 'completed' ? '✅' : step.icon}
            </div>

            {/* Step Content */}
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <h4 className={`font-semibold ${
                  step.status === 'active' ? 'text-yellow-300' : 'text-white'
                }`}>
                  {step.name}
                </h4>
                {step.status === 'active' && (
                  <div className="w-4 h-4 border-2 border-yellow-300 border-t-transparent rounded-full animate-spin"></div>
                )}
              </div>
              <p className="text-gray-400 text-sm">{step.description}</p>
            </div>

            {/* Progress Line */}
            {index < steps.length - 1 && (
              <div className={`w-px h-8 ${
                step.status === 'completed' ? 'bg-green-500' : 'bg-gray-600'
              }`}></div>
            )}
          </div>
        ))}
      </div>

      {/* Data Sources Display */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Web Sources */}
        {sources.length > 0 && (
          <div className="bg-white/5 rounded-lg p-4">
            <h4 className="text-white font-medium mb-3 flex items-center">
              <span className="mr-2">🌐</span>
              Web Sources
            </h4>
            <div className="space-y-2">
              {sources.map((source, index) => (
                <div key={index} className="text-sm text-gray-300 bg-white/10 rounded px-2 py-1">
                  {source}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* YouTube Videos */}
        {videos.length > 0 && (
          <div className="bg-white/5 rounded-lg p-4">
            <h4 className="text-white font-medium mb-3 flex items-center">
              <span className="mr-2">🎬</span>
              YouTube Videos
            </h4>
            <div className="space-y-2">
              {videos.map((video, index) => (
                <div key={index} className="text-sm text-gray-300 bg-white/10 rounded px-2 py-1">
                  {video}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Search Queries */}
        {queries.length > 0 && (
          <div className="bg-white/5 rounded-lg p-4">
            <h4 className="text-white font-medium mb-3 flex items-center">
              <span className="mr-2">🔍</span>
              Search Queries
            </h4>
            <div className="space-y-2">
              {queries.map((query, index) => (
                <div key={index} className="text-sm text-gray-300 bg-white/10 rounded px-2 py-1">
                  {query}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Overall Progress Bar */}
      <div className="mt-8">
        <div className="flex justify-between text-sm text-gray-300 mb-2">
          <span>Overall Progress</span>
          <span>{Math.round(((currentStep + 1) / steps.length) * 100)}%</span>
        </div>
        <div className="w-full bg-gray-700 rounded-full h-2">
          <div 
            className="bg-gradient-to-r from-yellow-400 to-orange-500 h-2 rounded-full transition-all duration-500"
            style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
          ></div>
        </div>
      </div>
    </div>
  );
}
